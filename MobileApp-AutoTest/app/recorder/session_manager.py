"""
Recording Session Manager Module

Manages individual recording sessions, tracking captured actions,
session state, and providing session-specific functionality.
"""

import time
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)


class RecordingSession:
    """Manages an individual recording session"""
    
    def __init__(self, device_id: str, session_id: str):
        self.device_id = device_id
        self.session_id = session_id
        self.start_time = datetime.now()
        self.end_time = None
        self.status = 'recording'
        self.captured_actions: List[Dict[str, Any]] = []
        self.metadata = {
            'platform': self._detect_platform(device_id),
            'session_type': 'recording',
            'version': '1.0.0'
        }
        
        logger.info(f"Created recording session {session_id} for device {device_id}")
    
    def add_action(self, action: Dict[str, Any]) -> bool:
        """
        Add a captured action to the session
        
        Args:
            action: The action data to add
            
        Returns:
            True if action added successfully, False otherwise
        """
        try:
            if self.status != 'recording':
                logger.warning(f"Cannot add action to session {self.session_id} - not recording")
                return False
            
            # Add sequence number
            action['sequence'] = len(self.captured_actions) + 1
            
            # Add session metadata
            action['session_id'] = self.session_id
            action['device_id'] = self.device_id
            
            self.captured_actions.append(action)
            
            logger.debug(f"Added action {action.get('action_id')} to session {self.session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding action to session: {str(e)}")
            return False
    
    def stop_recording(self) -> bool:
        """
        Stop the recording session
        
        Returns:
            True if stopped successfully, False otherwise
        """
        try:
            if self.status != 'recording':
                logger.warning(f"Session {self.session_id} is not recording")
                return False
            
            self.end_time = datetime.now()
            self.status = 'stopped'
            
            logger.info(f"Stopped recording session {self.session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping recording session: {str(e)}")
            return False
    
    def get_captured_actions(self) -> List[Dict[str, Any]]:
        """
        Get all captured actions from the session
        
        Returns:
            List of captured actions
        """
        return self.captured_actions.copy()
    
    def get_duration(self) -> float:
        """
        Get the duration of the recording session in seconds
        
        Returns:
            Duration in seconds
        """
        end_time = self.end_time or datetime.now()
        duration = (end_time - self.start_time).total_seconds()
        return round(duration, 2)
    
    def get_session_info(self) -> Dict[str, Any]:
        """
        Get comprehensive session information
        
        Returns:
            Dict containing session details
        """
        return {
            'session_id': self.session_id,
            'device_id': self.device_id,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'duration': self.get_duration(),
            'status': self.status,
            'actions_count': len(self.captured_actions),
            'metadata': self.metadata
        }
    
    def remove_action(self, action_id: str) -> bool:
        """
        Remove an action from the session
        
        Args:
            action_id: The ID of the action to remove
            
        Returns:
            True if action removed successfully, False otherwise
        """
        try:
            for i, action in enumerate(self.captured_actions):
                if action.get('action_id') == action_id:
                    del self.captured_actions[i]
                    # Update sequence numbers for remaining actions
                    for j, remaining_action in enumerate(self.captured_actions[i:], i):
                        remaining_action['sequence'] = j + 1
                    
                    logger.debug(f"Removed action {action_id} from session {self.session_id}")
                    return True
            
            logger.warning(f"Action {action_id} not found in session {self.session_id}")
            return False
            
        except Exception as e:
            logger.error(f"Error removing action from session: {str(e)}")
            return False
    
    def update_action(self, action_id: str, updates: Dict[str, Any]) -> bool:
        """
        Update an existing action in the session
        
        Args:
            action_id: The ID of the action to update
            updates: Dict containing fields to update
            
        Returns:
            True if action updated successfully, False otherwise
        """
        try:
            for action in self.captured_actions:
                if action.get('action_id') == action_id:
                    action.update(updates)
                    logger.debug(f"Updated action {action_id} in session {self.session_id}")
                    return True
            
            logger.warning(f"Action {action_id} not found in session {self.session_id}")
            return False
            
        except Exception as e:
            logger.error(f"Error updating action in session: {str(e)}")
            return False
    
    def get_action_by_id(self, action_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a specific action by its ID
        
        Args:
            action_id: The ID of the action to retrieve
            
        Returns:
            Action dict if found, None otherwise
        """
        for action in self.captured_actions:
            if action.get('action_id') == action_id:
                return action.copy()
        return None
    
    def clear_actions(self) -> bool:
        """
        Clear all captured actions from the session
        
        Returns:
            True if cleared successfully, False otherwise
        """
        try:
            self.captured_actions.clear()
            logger.info(f"Cleared all actions from session {self.session_id}")
            return True
        except Exception as e:
            logger.error(f"Error clearing actions from session: {str(e)}")
            return False
    
    def pause_recording(self) -> bool:
        """
        Pause the recording session
        
        Returns:
            True if paused successfully, False otherwise
        """
        try:
            if self.status != 'recording':
                logger.warning(f"Cannot pause session {self.session_id} - not recording")
                return False
            
            self.status = 'paused'
            logger.info(f"Paused recording session {self.session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error pausing recording session: {str(e)}")
            return False
    
    def resume_recording(self) -> bool:
        """
        Resume a paused recording session
        
        Returns:
            True if resumed successfully, False otherwise
        """
        try:
            if self.status != 'paused':
                logger.warning(f"Cannot resume session {self.session_id} - not paused")
                return False
            
            self.status = 'recording'
            logger.info(f"Resumed recording session {self.session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error resuming recording session: {str(e)}")
            return False
    
    def _detect_platform(self, device_id: str) -> str:
        """
        Detect platform based on device ID
        
        Args:
            device_id: The device identifier
            
        Returns:
            Platform string ('ios' or 'android')
        """
        # Simple platform detection based on device ID format
        # iOS device IDs are typically longer and contain hyphens
        if '-' in device_id and len(device_id) > 20:
            return 'ios'
        else:
            return 'android'
    
    def export_session_data(self) -> Dict[str, Any]:
        """
        Export complete session data for backup or analysis
        
        Returns:
            Complete session data dict
        """
        return {
            'session_info': self.get_session_info(),
            'actions': self.get_captured_actions(),
            'export_timestamp': datetime.now().isoformat()
        }
