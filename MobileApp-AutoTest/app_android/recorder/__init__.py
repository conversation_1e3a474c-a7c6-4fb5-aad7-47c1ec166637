"""
Mobile App Test Recorder Module - Android Platform

This module provides comprehensive test recording functionality for Android mobile applications.
It captures user interactions on device screens and converts them into executable test cases.

Features:
- Real-time device screen mirroring
- Interactive recording controls (Record, Stop, Play, Save)
- Action capture system for taps, swipes, text input, etc.
- Element identification with multiple locator strategies (UISelector, ID, XPath)
- Integration with existing test case infrastructure
- Android-specific optimizations and features

Author: Mobile Automation Team
Version: 1.0.0
Platform: Android
"""

__version__ = "1.0.0"
__author__ = "Mobile Automation Team"
__platform__ = "android"

# Import main recorder components
from .recorder_manager import RecorderManager
from .action_capture import ActionCapture
from .session_manager import RecordingSession

__all__ = [
    'RecorderManager',
    'ActionCapture', 
    'RecordingSession'
]
