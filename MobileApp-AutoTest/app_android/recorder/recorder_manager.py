"""
Recorder Manager Module - Android Platform

Handles the main recording functionality including session management,
device interaction capture, and integration with existing Android systems.
"""

import json
import os
import time
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from flask import session

from ..utils.id_generator import generate_action_id
from ..utils.database import get_db_connection
from .action_capture import ActionCapture
from .session_manager import RecordingSession

logger = logging.getLogger(__name__)


class RecorderManager:
    """Main recorder manager class for Android platform"""
    
    def __init__(self):
        self.active_sessions: Dict[str, RecordingSession] = {}
        self.action_capture = ActionCapture()
        self.platform = 'android'
        
    def start_recording_session(self, device_id: str, session_id: str) -> Dict[str, Any]:
        """
        Start a new recording session for the specified Android device
        
        Args:
            device_id: The Android device identifier
            session_id: The session identifier
            
        Returns:
            Dict containing session info and status
        """
        try:
            # Check if session already exists
            if session_id in self.active_sessions:
                return {
                    'success': False,
                    'error': 'Recording session already active'
                }
            
            # Create new recording session
            recording_session = RecordingSession(device_id, session_id)
            self.active_sessions[session_id] = recording_session
            
            logger.info(f"Started Android recording session {session_id} for device {device_id}")
            
            return {
                'success': True,
                'session_id': session_id,
                'device_id': device_id,
                'platform': self.platform,
                'start_time': recording_session.start_time,
                'status': 'recording'
            }
            
        except Exception as e:
            logger.error(f"Error starting Android recording session: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def stop_recording_session(self, session_id: str) -> Dict[str, Any]:
        """
        Stop an active Android recording session
        
        Args:
            session_id: The session identifier
            
        Returns:
            Dict containing session info and captured actions
        """
        try:
            if session_id not in self.active_sessions:
                return {
                    'success': False,
                    'error': 'No active recording session found'
                }
            
            recording_session = self.active_sessions[session_id]
            recording_session.stop_recording()
            
            # Get captured actions
            actions = recording_session.get_captured_actions()
            
            logger.info(f"Stopped Android recording session {session_id}, captured {len(actions)} actions")
            
            return {
                'success': True,
                'session_id': session_id,
                'platform': self.platform,
                'actions': actions,
                'duration': recording_session.get_duration(),
                'status': 'stopped'
            }
            
        except Exception as e:
            logger.error(f"Error stopping Android recording session: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def capture_action(self, session_id: str, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Capture a user action during Android recording
        
        Args:
            session_id: The session identifier
            action_data: The action data to capture
            
        Returns:
            Dict containing capture status and action info
        """
        try:
            if session_id not in self.active_sessions:
                return {
                    'success': False,
                    'error': 'No active recording session found'
                }
            
            recording_session = self.active_sessions[session_id]
            
            # Process the action through Android-specific action capture system
            processed_action = self.action_capture.process_action(action_data)
            
            # Add Android-specific metadata
            processed_action['platform'] = 'android'
            
            # Add to recording session
            recording_session.add_action(processed_action)
            
            return {
                'success': True,
                'action_id': processed_action.get('action_id'),
                'action_type': processed_action.get('type'),
                'timestamp': processed_action.get('timestamp'),
                'platform': 'android'
            }
            
        except Exception as e:
            logger.error(f"Error capturing Android action: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def save_test_case(self, session_id: str, test_case_name: str, 
                      test_case_dir: str) -> Dict[str, Any]:
        """
        Save recorded Android session as a test case
        
        Args:
            session_id: The session identifier
            test_case_name: Name for the test case
            test_case_dir: Directory to save the test case
            
        Returns:
            Dict containing save status and file info
        """
        try:
            if session_id not in self.active_sessions:
                return {
                    'success': False,
                    'error': 'No recording session found'
                }
            
            recording_session = self.active_sessions[session_id]
            actions = recording_session.get_captured_actions()
            
            if not actions:
                return {
                    'success': False,
                    'error': 'No actions recorded'
                }
            
            # Create Android test case data structure
            test_case_data = {
                'name': test_case_name,
                'created': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'device_id': recording_session.device_id,
                'platform': 'android',
                'actions': actions
            }
            
            # Generate filename with timestamp
            timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
            filename = f"{test_case_name.replace(' ', '_')}_{timestamp}.json"
            filepath = os.path.join(test_case_dir, filename)
            
            # Save to file
            with open(filepath, 'w') as f:
                json.dump(test_case_data, f, indent=2)
            
            # Clean up session
            del self.active_sessions[session_id]
            
            logger.info(f"Saved Android test case '{test_case_name}' to {filepath}")
            
            return {
                'success': True,
                'filename': filename,
                'filepath': filepath,
                'actions_count': len(actions),
                'test_case_name': test_case_name,
                'platform': 'android'
            }
            
        except Exception as e:
            logger.error(f"Error saving Android test case: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_session_status(self, session_id: str) -> Dict[str, Any]:
        """
        Get status of an Android recording session
        
        Args:
            session_id: The session identifier
            
        Returns:
            Dict containing session status info
        """
        if session_id not in self.active_sessions:
            return {
                'active': False,
                'error': 'Session not found'
            }
        
        recording_session = self.active_sessions[session_id]
        
        return {
            'active': True,
            'session_id': session_id,
            'device_id': recording_session.device_id,
            'platform': self.platform,
            'start_time': recording_session.start_time,
            'duration': recording_session.get_duration(),
            'actions_count': len(recording_session.get_captured_actions()),
            'status': recording_session.status
        }
    
    def cleanup_session(self, session_id: str) -> bool:
        """
        Clean up an Android recording session
        
        Args:
            session_id: The session identifier
            
        Returns:
            True if cleanup successful, False otherwise
        """
        try:
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]
                logger.info(f"Cleaned up Android recording session {session_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"Error cleaning up Android session: {str(e)}")
            return False
    
    def get_android_specific_capabilities(self) -> Dict[str, Any]:
        """
        Get Android-specific recording capabilities
        
        Returns:
            Dict containing Android capabilities
        """
        return {
            'supported_locators': ['id', 'xpath', 'uiselector', 'accessibility_id'],
            'supported_actions': [
                'tap', 'double_tap', 'long_press', 'swipe', 'text_input',
                'scroll', 'pinch', 'rotate', 'back_button', 'home_button',
                'menu_button', 'volume_up', 'volume_down'
            ],
            'platform_features': [
                'uiautomator2_integration',
                'webview_context_switching',
                'native_app_support',
                'hybrid_app_support'
            ]
        }
