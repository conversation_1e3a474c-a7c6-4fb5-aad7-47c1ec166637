# Mobile App Automation Tool - Complete Docker Deployment Guide

This comprehensive guide provides everything you need to containerize and deploy your Mobile App Automation Tool using Docker, enabling simultaneous access to both iOS and Android testing platforms.

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose installed
- At least 4GB RAM available for the container
- Ports 8080, 8081, 4723, 4724 available on your host machine

### One-Command Deployment
```bash
./deploy-docker.sh
```

This script will:
1. Create all necessary host directories
2. Build the Docker image with all dependencies
3. Start the container with both platforms
4. Perform health checks
5. Display access information

## 📋 What's Included in the Container

### System Dependencies
- **Ubuntu 22.04** base image
- **Python 3.10** with virtual environment
- **Node.js 18.x** and npm
- **Java 11 OpenJDK** (required for Appium and Android)
- **Android SDK** with platform-tools and build-tools
- **Tesseract OCR** for text recognition
- **OpenCV dependencies** for image processing
- **Supervisor** for process management

### Python Packages
All packages from your `requirements.txt` including:
- Flask and Flask-SocketIO for web interfaces
- Appium-Python-Client for device automation
- OpenCV, PIL, scikit-image for image processing
- facebook-wda and tidevice for iOS support
- uiautomator2 and adbutils for Android support
- All other dependencies preserved exactly as in your app

### Node.js Components
- **Appium 2.x** with inspector plugin
- **XCUITest driver** for iOS automation
- **UiAutomator2 driver** for Android automation
- All packages from your `package.json`

## 🏗️ Container Architecture

### Service Layout
```
┌─────────────────────────────────────────────────────────────┐
│                    Docker Container                         │
├─────────────────────────────────────────────────────────────┤
│  Supervisor Process Manager                                 │
│  ┌─────────────────┐  ┌─────────────────┐                 │
│  │   iOS Platform  │  │ Android Platform│                 │
│  │   Port 8080     │  │   Port 8081     │                 │
│  │   (run.py)      │  │ (run_android.py)│                 │
│  └─────────────────┘  └─────────────────┘                 │
│  ┌─────────────────┐  ┌─────────────────┐                 │
│  │ Appium iOS      │  │ Appium Android  │                 │
│  │ Port 4723       │  │ Port 4724       │                 │
│  └─────────────────┘  └─────────────────┘                 │
└─────────────────────────────────────────────────────────────┘
```

### Volume Mounts
```
Host Machine                 Container
├── host-data/
│   ├── test_cases/     →    /app/test_cases
│   ├── test_suites/    →    /app/test_suites
│   ├── reports/        →    /app/reports
│   ├── screenshots/    →    /app/screenshots
│   ├── reference_images/ →  /app/reference_images
│   ├── recordings/     →    /app/recordings
│   ├── temp/           →    /app/temp
│   ├── files_to_push/  →    /app/files_to_push
│   ├── app-data/       →    /app/app/data
│   ├── app-android-data/ →  /app/app_android/data
│   └── data/           →    /app/data
```

## 🔧 Deployment Options

### Option 1: Automated Deployment (Recommended)
```bash
# Full deployment with health checks
./deploy-docker.sh

# Build only
./deploy-docker.sh build

# Start existing image
./deploy-docker.sh start

# View logs
./deploy-docker.sh logs

# Stop container
./deploy-docker.sh stop
```

### Option 2: Docker Compose
```bash
# Build and start
docker-compose up --build -d

# View logs
docker-compose logs -f

# Stop
docker-compose down
```

### Option 3: Manual Docker Commands
```bash
# Build image
docker build -t mobile-automation-tool .

# Run container with volume mounts
docker run -d \
  --name mobile-automation-tool \
  --network host \
  -v $(pwd)/host-data/test_cases:/app/test_cases \
  -v $(pwd)/host-data/test_suites:/app/test_suites \
  -v $(pwd)/host-data/reports:/app/reports \
  -v $(pwd)/host-data/screenshots:/app/screenshots \
  -v $(pwd)/host-data/reference_images:/app/reference_images \
  -v $(pwd)/host-data/recordings:/app/recordings \
  -v $(pwd)/host-data/temp:/app/temp \
  -v $(pwd)/host-data/files_to_push:/app/files_to_push \
  -v $(pwd)/host-data/app-data:/app/app/data \
  -v $(pwd)/host-data/app-android-data:/app/app_android/data \
  -v $(pwd)/host-data/data:/app/data \
  mobile-automation-tool
```

## 🌐 Access URLs

Once deployed, access your platforms at:

| Platform | URL | Description |
|----------|-----|-------------|
| **iOS Testing** | http://localhost:8080 | Complete iOS automation interface |
| **Android Testing** | http://localhost:8081 | Complete Android automation interface |
| **Appium iOS** | http://localhost:4723 | Appium server for iOS devices |
| **Appium Android** | http://localhost:4724 | Appium server for Android devices |

## 📁 Volume Configuration

### Setting Up Host Directories

The deployment script automatically creates these directories:

```bash
host-data/
├── test_cases/          # Your test case JSON files
├── test_suites/         # Test suite configurations
├── reports/             # Generated HTML/JSON reports
├── screenshots/         # Screenshots during test execution
├── reference_images/    # Images for image matching actions
├── recordings/          # Test execution recordings
├── temp/               # Temporary files and debug images
├── files_to_push/      # Files to push to devices during tests
├── app-data/           # iOS platform SQLite databases
├── app-android-data/   # Android platform SQLite databases
└── data/               # Shared database files
```

### Configuring Paths in the Application

After container startup:

1. **Access iOS Platform**: http://localhost:8080
2. **Go to Settings Tab**
3. **Configure folder paths**:
   - Test Cases Folder: `/app/test_cases`
   - Test Suites Folder: `/app/test_suites`
   - Reports Folder: `/app/reports`
   - Screenshots Folder: `/app/screenshots`
   - Reference Images Folder: `/app/reference_images`
   - Recordings Folder: `/app/recordings`
   - Temp Files Folder: `/app/temp`
   - Files to Push Folder: `/app/files_to_push`

4. **Repeat for Android Platform**: http://localhost:8081

### Example: Adding Your Existing Test Cases

```bash
# Copy your existing test cases to the mounted directory
cp -r /path/to/your/test_cases/* host-data/test_cases/

# Copy your test suites
cp -r /path/to/your/test_suites/* host-data/test_suites/

# Copy reference images
cp -r /path/to/your/reference_images/* host-data/reference_images/
```

## 📱 Device Connectivity

### iOS Device Setup

**Requirements**: macOS host machine (iOS testing requires macOS)

1. **Connect iOS device** to your Mac via USB
2. **Trust the device** when prompted
3. **Ensure device appears in Xcode** (if you have Xcode installed)
4. **Container uses host network** to access the device

The container will automatically detect iOS devices connected to the host machine.

### Android Device Setup

**Works on**: macOS, Linux, Windows

#### USB Connection
1. **Enable Developer Options** on your Android device
2. **Enable USB Debugging**
3. **Connect device via USB**
4. **Accept debugging authorization** when prompted
5. **Verify connection**: `adb devices` (from host machine)

#### Network ADB (Wireless)
1. **Connect device to same network** as host machine
2. **Enable network debugging** on device
3. **Connect via ADB**: `adb connect <device-ip>:5555`

The container accesses Android devices through the host's ADB daemon.

## 🔍 Monitoring and Troubleshooting

### Health Checks

The container includes built-in health monitoring:

```bash
# Check container health
docker-compose ps

# View health check logs
docker-compose logs mobile-automation-tool | grep -i health
```

### Viewing Logs

```bash
# All services
docker-compose logs -f

# Specific service logs
docker exec -it mobile-automation-tool tail -f /var/log/supervisor/ios-app.out.log
docker exec -it mobile-automation-tool tail -f /var/log/supervisor/android-app.out.log
docker exec -it mobile-automation-tool tail -f /var/log/supervisor/appium-ios.out.log
docker exec -it mobile-automation-tool tail -f /var/log/supervisor/appium-android.out.log
```

### Common Issues and Solutions

#### Container Won't Start
```bash
# Check Docker logs
docker-compose logs

# Verify port availability
netstat -tulpn | grep -E ':(8080|8081|4723|4724)'

# Check disk space
df -h
```

#### Web Interface Not Accessible
```bash
# Verify container is running
docker-compose ps

# Check if services are listening
docker exec -it mobile-automation-tool netstat -tulpn | grep -E ':(8080|8081)'

# Test from inside container
docker exec -it mobile-automation-tool curl http://localhost:8080
```

#### Device Not Detected

**For iOS**:
```bash
# Check device on host
idevice_id -l

# Check from container (should see same devices)
docker exec -it mobile-automation-tool idevice_id -l
```

**For Android**:
```bash
# Check device on host
adb devices

# Check from container
docker exec -it mobile-automation-tool adb devices
```

#### Database Issues
```bash
# Check database files
ls -la host-data/app-data/
ls -la host-data/app-android-data/

# Check permissions
docker exec -it mobile-automation-tool ls -la /app/app/data/
```

### Performance Optimization

#### Resource Allocation
```yaml
# In docker-compose.yml, add:
deploy:
  resources:
    limits:
      cpus: '4.0'
      memory: 8G
    reservations:
      cpus: '2.0'
      memory: 4G
```

#### Storage Optimization
```bash
# Clean up unused Docker resources
docker system prune -f

# Remove old images
docker image prune -f
```

## 🔒 Security Considerations

### Container Security
- Container runs with necessary privileges for device access
- All services run within isolated container environment
- Host directories are mounted with appropriate permissions

### Network Security
- Uses host network mode for device connectivity
- Consider firewall rules for exposed ports
- Limit access to trusted networks only

### Data Security
- Database files are stored in mounted volumes
- Ensure host directory permissions are appropriate
- Consider encrypting sensitive test data

## 🚀 Advanced Configuration

### Custom Environment Variables

Add to `docker-compose.yml`:
```yaml
environment:
  - FLASK_DEBUG=false
  - APPIUM_LOG_LEVEL=info
  - CUSTOM_CONFIG_PATH=/app/custom-config
```

### Multiple Instances

Run multiple containers on different ports:
```bash
# Copy docker-compose.yml to docker-compose-instance2.yml
# Modify ports (e.g., 8082, 8083, 4725, 4726)
docker-compose -f docker-compose-instance2.yml up -d
```

### Custom Appium Configuration

Mount custom Appium config:
```yaml
volumes:
  - ./custom-appium-config.json:/app/appium-config.json
```

### SSL/HTTPS Setup

For production deployment with SSL:
1. Add SSL certificates to container
2. Configure Flask for HTTPS
3. Update port mappings for 443

## 📊 Monitoring and Metrics

### Container Metrics
```bash
# Resource usage
docker stats mobile-automation-tool

# Detailed container info
docker inspect mobile-automation-tool
```

### Application Metrics
- Access built-in Flask metrics at each platform
- Monitor test execution through web interfaces
- Check Appium server status via REST API

## 🔄 Updates and Maintenance

### Updating the Container
```bash
# Pull latest changes
git pull

# Rebuild and restart
docker-compose down
docker-compose up --build -d
```

### Backup and Restore
```bash
# Backup host data
tar -czf backup-$(date +%Y%m%d).tar.gz host-data/

# Restore from backup
tar -xzf backup-20231201.tar.gz
```

### Database Migration
```bash
# Export data before updates
docker exec -it mobile-automation-tool python /app/scripts/export_data.py

# Import after updates
docker exec -it mobile-automation-tool python /app/scripts/import_data.py
```

## 🎯 Best Practices

### Development Workflow
1. **Test locally** before containerizing
2. **Use volume mounts** for active development
3. **Version your test cases** in git
4. **Regular backups** of test data

### Production Deployment
1. **Use specific image tags** instead of latest
2. **Set resource limits** appropriately
3. **Monitor container health** continuously
4. **Implement log rotation**

### Test Organization
1. **Organize test cases** in logical folders
2. **Use descriptive naming** conventions
3. **Maintain reference images** separately
4. **Document test dependencies**

## 📞 Support and Troubleshooting

### Getting Help
1. **Check container logs** first
2. **Verify host machine setup**
3. **Test device connectivity** outside container
4. **Review volume mount configuration**

### Common Commands Reference
```bash
# Container management
docker-compose up -d          # Start in background
docker-compose down           # Stop and remove
docker-compose restart        # Restart services
docker-compose ps             # Check status

# Debugging
docker-compose logs -f        # Follow logs
docker exec -it mobile-automation-tool bash  # Shell access
docker-compose exec mobile-automation-tool python --version

# Cleanup
docker system prune -f        # Clean unused resources
docker volume prune -f        # Clean unused volumes
```

This Docker solution provides a complete, production-ready containerization of your Mobile App Automation Tool while preserving all functionality and enabling easy deployment across different environments.