# Mobile App Automation Tool - Docker Container
# Supports both iOS and Android testing platforms

FROM ubuntu:22.04

# Avoid interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV NODE_VERSION=18.x
ENV JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64
ENV ANDROID_HOME=/opt/android-sdk
ENV ANDROID_SDK_ROOT=/opt/android-sdk
ENV PATH=$PATH:$ANDROID_HOME/platform-tools:$ANDROID_HOME/build-tools/33.0.0

# Create app directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    # Basic system tools
    curl \
    wget \
    unzip \
    git \
    build-essential \
    software-properties-common \
    apt-transport-https \
    ca-certificates \
    gnupg \
    lsb-release \
    # Python and pip
    python3 \
    python3-pip \
    python3-venv \
    python3-dev \
    # Java (required for Appium and Android)
    openjdk-11-jdk \
    # Image processing libraries (for OpenCV and PIL)
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    libgtk-3-0 \
    libavcodec-dev \
    libavformat-dev \
    libswscale-dev \
    libv4l-dev \
    libxvidcore-dev \
    libx264-dev \
    libjpeg-dev \
    libpng-dev \
    libtiff-dev \
    libatlas-base-dev \
    gfortran \
    # Tesseract OCR
    tesseract-ocr \
    tesseract-ocr-eng \
    libtesseract-dev \
    # Additional libraries for Python packages
    libffi-dev \
    libssl-dev \
    libxml2-dev \
    libxslt1-dev \
    libjpeg8-dev \
    zlib1g-dev \
    libfreetype6-dev \
    liblcms2-dev \
    libwebp-dev \
    tcl8.6-dev \
    tk8.6-dev \
    python3-tk \
    # For gevent compilation
    libevent-dev \
    # For psycopg2
    libpq-dev \
    # Process management
    supervisor \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js and npm
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs

# Install Android SDK
RUN mkdir -p ${ANDROID_HOME} \
    && cd ${ANDROID_HOME} \
    && wget -q https://dl.google.com/android/repository/commandlinetools-linux-9477386_latest.zip \
    && unzip commandlinetools-linux-9477386_latest.zip \
    && rm commandlinetools-linux-9477386_latest.zip \
    && mkdir -p cmdline-tools/latest \
    && mv cmdline-tools/* cmdline-tools/latest/ || true \
    && yes | cmdline-tools/latest/bin/sdkmanager --licenses \
    && cmdline-tools/latest/bin/sdkmanager "platform-tools" "build-tools;33.0.0" "platforms;android-33"

# Create Python virtual environment
RUN python3 -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Upgrade pip in virtual environment
RUN pip install --upgrade pip setuptools wheel

# Copy requirements and package files first (for better Docker layer caching)
COPY requirements.txt package.json package-lock.json ./

# Install Python dependencies
RUN pip install Cython==3.0.9
RUN pip install -r requirements.txt

# Install Node.js dependencies
RUN npm install

# Verify Appium drivers are installed (they should be installed via npm install from package.json)
RUN npx appium driver list --installed

# Copy the entire application
COPY . .

# Create necessary directories for volume mounting
RUN mkdir -p \
    /app/test_cases \
    /app/test_suites \
    /app/reports \
    /app/screenshots \
    /app/reference_images \
    /app/recordings \
    /app/temp \
    /app/files_to_push \
    /app/app/data \
    /app/app_android/data \
    /app/data

# Create supervisor configuration for running both platforms
RUN mkdir -p /etc/supervisor/conf.d

# Create supervisor configuration file
COPY docker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Create startup script
COPY docker/start.sh /start.sh
RUN chmod +x /start.sh

# Create health check script
COPY docker/healthcheck.sh /healthcheck.sh
RUN chmod +x /healthcheck.sh

# Expose ports for both platforms
EXPOSE 8080 8081 4723 4724

# Add health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD /healthcheck.sh

# Set the startup command
CMD ["/start.sh"]