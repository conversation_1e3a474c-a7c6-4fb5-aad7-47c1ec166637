"""
Action Capture Module

Handles the capture and processing of user interactions during recording sessions.
Converts raw interaction data into structured action format compatible with the test execution engine.
"""

import time
import logging
from typing import Dict, List, Optional, Any, Tuple
from utils.id_generator import generate_action_id

logger = logging.getLogger(__name__)


class ActionCapture:
    """Handles capture and processing of user actions during recording"""
    
    def __init__(self):
        self.supported_actions = {
            'tap': self._process_tap_action,
            'double_tap': self._process_double_tap_action,
            'long_press': self._process_long_press_action,
            'swipe': self._process_swipe_action,
            'text_input': self._process_text_input_action,
            'scroll': self._process_scroll_action,
            'pinch': self._process_pinch_action,
            'rotate': self._process_rotate_action
        }
    
    def process_action(self, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process raw action data into structured format
        
        Args:
            action_data: Raw action data from user interaction
            
        Returns:
            Processed action in test case format
        """
        try:
            action_type = action_data.get('type', 'unknown')
            
            if action_type not in self.supported_actions:
                logger.warning(f"Unsupported action type: {action_type}")
                return self._create_unknown_action(action_data)
            
            # Process the action using the appropriate handler
            processor = self.supported_actions[action_type]
            processed_action = processor(action_data)
            
            # Add common fields
            processed_action['action_id'] = generate_action_id()
            processed_action['timestamp'] = int(time.time() * 1000)
            
            logger.debug(f"Processed {action_type} action: {processed_action['action_id']}")
            
            return processed_action
            
        except Exception as e:
            logger.error(f"Error processing action: {str(e)}")
            return self._create_error_action(action_data, str(e))
    
    def _process_tap_action(self, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process tap/click action"""
        x = action_data.get('x', 0)
        y = action_data.get('y', 0)
        
        # Try to identify element at coordinates
        element_info = self._identify_element(x, y, action_data.get('page_source'))
        
        action = {
            'type': 'tap',
            'method': 'coordinates',
            'x': x,
            'y': y,
            'timeout': 10,
            'interval': 0.5
        }
        
        # Add element identification if available
        if element_info:
            action.update(element_info)
            if element_info.get('locator_type') and element_info.get('locator_value'):
                action['method'] = 'locator'
        
        return action
    
    def _process_double_tap_action(self, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process double tap action"""
        x = action_data.get('x', 0)
        y = action_data.get('y', 0)
        
        element_info = self._identify_element(x, y, action_data.get('page_source'))
        
        action = {
            'type': 'doubleTap',
            'method': 'coordinates',
            'x': x,
            'y': y,
            'timeout': 10
        }
        
        if element_info:
            action.update(element_info)
            if element_info.get('locator_type') and element_info.get('locator_value'):
                action['method'] = 'locator'
        
        return action
    
    def _process_long_press_action(self, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process long press action"""
        x = action_data.get('x', 0)
        y = action_data.get('y', 0)
        duration = action_data.get('duration', 1000)
        
        element_info = self._identify_element(x, y, action_data.get('page_source'))
        
        action = {
            'type': 'longPress',
            'method': 'coordinates',
            'x': x,
            'y': y,
            'duration': duration,
            'timeout': 10
        }
        
        if element_info:
            action.update(element_info)
            if element_info.get('locator_type') and element_info.get('locator_value'):
                action['method'] = 'locator'
        
        return action
    
    def _process_swipe_action(self, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process swipe gesture"""
        start_x = action_data.get('start_x', 0)
        start_y = action_data.get('start_y', 0)
        end_x = action_data.get('end_x', 0)
        end_y = action_data.get('end_y', 0)
        duration = action_data.get('duration', 1000)
        
        # Determine swipe direction
        direction = self._calculate_swipe_direction(start_x, start_y, end_x, end_y)
        
        action = {
            'type': 'swipe',
            'direction': direction,
            'start_x': start_x,
            'start_y': start_y,
            'end_x': end_x,
            'end_y': end_y,
            'duration': duration,
            'timeout': 10
        }
        
        return action
    
    def _process_text_input_action(self, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process text input action"""
        text = action_data.get('text', '')
        x = action_data.get('x', 0)
        y = action_data.get('y', 0)
        clear_first = action_data.get('clear_first', False)
        
        element_info = self._identify_element(x, y, action_data.get('page_source'))
        
        action = {
            'type': 'sendKeys',
            'text': text,
            'clear_first': clear_first,
            'timeout': 10
        }
        
        if element_info:
            action.update(element_info)
        
        return action
    
    def _process_scroll_action(self, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process scroll action"""
        direction = action_data.get('direction', 'down')
        distance = action_data.get('distance', 500)
        
        action = {
            'type': 'scroll',
            'direction': direction,
            'distance': distance,
            'timeout': 10
        }
        
        return action
    
    def _process_pinch_action(self, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process pinch gesture"""
        scale = action_data.get('scale', 1.0)
        center_x = action_data.get('center_x', 0)
        center_y = action_data.get('center_y', 0)
        
        action = {
            'type': 'pinch',
            'scale': scale,
            'center_x': center_x,
            'center_y': center_y,
            'timeout': 10
        }
        
        return action
    
    def _process_rotate_action(self, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process rotation gesture"""
        angle = action_data.get('angle', 0)
        center_x = action_data.get('center_x', 0)
        center_y = action_data.get('center_y', 0)
        
        action = {
            'type': 'rotate',
            'angle': angle,
            'center_x': center_x,
            'center_y': center_y,
            'timeout': 10
        }
        
        return action
    
    def _identify_element(self, x: int, y: int, page_source: Optional[str] = None) -> Dict[str, Any]:
        """
        Attempt to identify element at given coordinates using multiple strategies

        Args:
            x: X coordinate
            y: Y coordinate
            page_source: Optional page source for element identification

        Returns:
            Dict containing element identification info
        """
        element_info = {}

        if page_source:
            try:
                # Try multiple identification strategies in order of preference
                strategies = [
                    self._identify_by_accessibility_id,
                    self._identify_by_id,
                    self._identify_by_class_name,
                    self._identify_by_xpath
                ]

                for strategy in strategies:
                    result = strategy(x, y, page_source)
                    if result and result.get('locator_value'):
                        element_info.update(result)
                        element_info['identification_method'] = strategy.__name__
                        break

                # Add confidence score based on identification method
                if element_info:
                    element_info['confidence'] = self._calculate_confidence(element_info)

            except Exception as e:
                logger.error(f"Error identifying element: {str(e)}")

        return element_info

    def _identify_by_accessibility_id(self, x: int, y: int, page_source: str) -> Dict[str, Any]:
        """
        Identify element by accessibility ID

        Args:
            x: X coordinate
            y: Y coordinate
            page_source: Page source XML

        Returns:
            Dict containing identification info
        """
        try:
            # This would parse the page source and find elements with accessibility IDs
            # at or near the given coordinates
            # For now, return placeholder
            return {}
        except Exception as e:
            logger.error(f"Error identifying by accessibility ID: {str(e)}")
            return {}

    def _identify_by_id(self, x: int, y: int, page_source: str) -> Dict[str, Any]:
        """
        Identify element by ID attribute

        Args:
            x: X coordinate
            y: Y coordinate
            page_source: Page source XML

        Returns:
            Dict containing identification info
        """
        try:
            # This would parse the page source and find elements with IDs
            # at or near the given coordinates
            # For now, return placeholder
            return {}
        except Exception as e:
            logger.error(f"Error identifying by ID: {str(e)}")
            return {}

    def _identify_by_class_name(self, x: int, y: int, page_source: str) -> Dict[str, Any]:
        """
        Identify element by class name

        Args:
            x: X coordinate
            y: Y coordinate
            page_source: Page source XML

        Returns:
            Dict containing identification info
        """
        try:
            # This would parse the page source and find elements by class
            # at or near the given coordinates
            # For now, return placeholder
            return {}
        except Exception as e:
            logger.error(f"Error identifying by class name: {str(e)}")
            return {}

    def _identify_by_xpath(self, x: int, y: int, page_source: str) -> Dict[str, Any]:
        """
        Generate XPath for element at coordinates

        Args:
            x: X coordinate
            y: Y coordinate
            page_source: Page source XML

        Returns:
            Dict containing identification info
        """
        try:
            # This would generate an XPath expression for the element
            # at the given coordinates
            # For now, return placeholder
            return {}
        except Exception as e:
            logger.error(f"Error generating XPath: {str(e)}")
            return {}

    def _calculate_confidence(self, element_info: Dict[str, Any]) -> float:
        """
        Calculate confidence score for element identification

        Args:
            element_info: Element identification info

        Returns:
            Confidence score between 0.0 and 1.0
        """
        try:
            locator_type = element_info.get('locator_type', '')

            # Assign confidence based on locator type reliability
            confidence_scores = {
                'accessibility_id': 0.9,
                'id': 0.8,
                'class_name': 0.6,
                'xpath': 0.4
            }

            base_confidence = confidence_scores.get(locator_type, 0.3)

            # Adjust confidence based on other factors
            if element_info.get('locator_value'):
                # Longer, more specific locators are generally more reliable
                locator_length = len(element_info['locator_value'])
                if locator_length > 20:
                    base_confidence += 0.1
                elif locator_length < 5:
                    base_confidence -= 0.1

            return min(max(base_confidence, 0.0), 1.0)

        except Exception as e:
            logger.error(f"Error calculating confidence: {str(e)}")
            return 0.0
    
    def _calculate_swipe_direction(self, start_x: int, start_y: int, 
                                 end_x: int, end_y: int) -> str:
        """Calculate swipe direction from coordinates"""
        dx = end_x - start_x
        dy = end_y - start_y
        
        if abs(dx) > abs(dy):
            return 'right' if dx > 0 else 'left'
        else:
            return 'down' if dy > 0 else 'up'
    
    def _create_unknown_action(self, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create action for unknown action type"""
        return {
            'type': 'unknown',
            'raw_data': action_data,
            'error': 'Unsupported action type'
        }
    
    def _create_error_action(self, action_data: Dict[str, Any], error: str) -> Dict[str, Any]:
        """Create action for processing errors"""
        return {
            'type': 'error',
            'raw_data': action_data,
            'error': error,
            'action_id': generate_action_id(),
            'timestamp': int(time.time() * 1000)
        }

    def optimize_actions(self, actions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Optimize a sequence of captured actions by removing redundant actions
        and merging similar actions

        Args:
            actions: List of captured actions

        Returns:
            Optimized list of actions
        """
        if not actions:
            return actions

        try:
            optimized_actions = []
            i = 0

            while i < len(actions):
                current_action = actions[i]

                # Check for redundant consecutive taps
                if (current_action.get('type') == 'tap' and
                    i + 1 < len(actions) and
                    actions[i + 1].get('type') == 'tap'):

                    next_action = actions[i + 1]
                    if self._are_actions_similar(current_action, next_action):
                        # Skip the redundant action
                        logger.debug(f"Skipping redundant tap action {next_action.get('action_id')}")
                        i += 2
                        optimized_actions.append(current_action)
                        continue

                # Check for text input optimization
                if current_action.get('type') == 'sendKeys':
                    merged_action = self._merge_text_inputs(actions, i)
                    if merged_action:
                        optimized_actions.append(merged_action['action'])
                        i = merged_action['next_index']
                        continue

                # Check for swipe optimization
                if current_action.get('type') == 'swipe':
                    merged_action = self._merge_swipes(actions, i)
                    if merged_action:
                        optimized_actions.append(merged_action['action'])
                        i = merged_action['next_index']
                        continue

                optimized_actions.append(current_action)
                i += 1

            logger.info(f"Optimized {len(actions)} actions to {len(optimized_actions)} actions")
            return optimized_actions

        except Exception as e:
            logger.error(f"Error optimizing actions: {str(e)}")
            return actions

    def _are_actions_similar(self, action1: Dict[str, Any], action2: Dict[str, Any]) -> bool:
        """
        Check if two actions are similar enough to be considered redundant

        Args:
            action1: First action
            action2: Second action

        Returns:
            True if actions are similar, False otherwise
        """
        try:
            if action1.get('type') != action2.get('type'):
                return False

            # For tap actions, check coordinate proximity
            if action1.get('type') == 'tap':
                x1, y1 = action1.get('x', 0), action1.get('y', 0)
                x2, y2 = action2.get('x', 0), action2.get('y', 0)

                distance = ((x2 - x1) ** 2 + (y2 - y1) ** 2) ** 0.5
                return distance < 10  # Within 10 pixels

            # For other actions, check if they have the same locator
            locator1 = (action1.get('locator_type'), action1.get('locator_value'))
            locator2 = (action2.get('locator_type'), action2.get('locator_value'))

            return locator1 == locator2 and locator1[0] is not None

        except Exception as e:
            logger.error(f"Error comparing actions: {str(e)}")
            return False

    def _merge_text_inputs(self, actions: List[Dict[str, Any]], start_index: int) -> Optional[Dict[str, Any]]:
        """
        Merge consecutive text input actions into a single action

        Args:
            actions: List of all actions
            start_index: Starting index for merging

        Returns:
            Dict with merged action and next index, or None if no merge possible
        """
        try:
            current_action = actions[start_index]
            if current_action.get('type') != 'sendKeys':
                return None

            merged_text = current_action.get('text', '')
            next_index = start_index + 1

            # Look for consecutive text inputs to the same element
            while next_index < len(actions):
                next_action = actions[next_index]

                if (next_action.get('type') == 'sendKeys' and
                    self._are_actions_similar(current_action, next_action)):

                    merged_text += next_action.get('text', '')
                    next_index += 1
                else:
                    break

            if next_index > start_index + 1:
                # Create merged action
                merged_action = current_action.copy()
                merged_action['text'] = merged_text
                merged_action['action_id'] = generate_action_id()
                merged_action['merged_from'] = [
                    actions[i].get('action_id') for i in range(start_index, next_index)
                ]

                return {
                    'action': merged_action,
                    'next_index': next_index
                }

            return None

        except Exception as e:
            logger.error(f"Error merging text inputs: {str(e)}")
            return None

    def _merge_swipes(self, actions: List[Dict[str, Any]], start_index: int) -> Optional[Dict[str, Any]]:
        """
        Merge consecutive swipe actions in the same direction

        Args:
            actions: List of all actions
            start_index: Starting index for merging

        Returns:
            Dict with merged action and next index, or None if no merge possible
        """
        try:
            current_action = actions[start_index]
            if current_action.get('type') != 'swipe':
                return None

            current_direction = current_action.get('direction')
            next_index = start_index + 1

            # Look for consecutive swipes in the same direction
            while next_index < len(actions):
                next_action = actions[next_index]

                if (next_action.get('type') == 'swipe' and
                    next_action.get('direction') == current_direction):
                    next_index += 1
                else:
                    break

            if next_index > start_index + 1:
                # Create merged swipe action
                merged_action = current_action.copy()
                merged_action['action_id'] = generate_action_id()
                merged_action['merged_count'] = next_index - start_index
                merged_action['merged_from'] = [
                    actions[i].get('action_id') for i in range(start_index, next_index)
                ]

                return {
                    'action': merged_action,
                    'next_index': next_index
                }

            return None

        except Exception as e:
            logger.error(f"Error merging swipes: {str(e)}")
            return None

    def validate_action_sequence(self, actions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Validate a sequence of actions for potential issues

        Args:
            actions: List of actions to validate

        Returns:
            Dict containing validation results
        """
        try:
            issues = []
            warnings = []

            for i, action in enumerate(actions):
                # Check for missing required fields
                if not action.get('action_id'):
                    issues.append(f"Action {i}: Missing action_id")

                if not action.get('type'):
                    issues.append(f"Action {i}: Missing action type")

                # Check for suspicious coordinate values
                if action.get('type') in ['tap', 'swipe', 'longPress']:
                    x, y = action.get('x'), action.get('y')
                    if x is not None and y is not None:
                        if x < 0 or y < 0:
                            issues.append(f"Action {i}: Negative coordinates ({x}, {y})")
                        elif x > 10000 or y > 10000:
                            warnings.append(f"Action {i}: Unusually large coordinates ({x}, {y})")

                # Check for very short time intervals between actions
                if i > 0:
                    prev_timestamp = actions[i-1].get('timestamp', 0)
                    curr_timestamp = action.get('timestamp', 0)

                    if curr_timestamp - prev_timestamp < 50:  # Less than 50ms
                        warnings.append(f"Action {i}: Very short interval from previous action")

            return {
                'valid': len(issues) == 0,
                'issues': issues,
                'warnings': warnings,
                'total_actions': len(actions)
            }

        except Exception as e:
            logger.error(f"Error validating action sequence: {str(e)}")
            return {
                'valid': False,
                'issues': [f"Validation error: {str(e)}"],
                'warnings': [],
                'total_actions': len(actions)
            }
