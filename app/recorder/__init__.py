"""
Mobile App Test Recorder Module

This module provides comprehensive test recording functionality for mobile applications.
It captures user interactions on device screens and converts them into executable test cases.

Features:
- Real-time device screen mirroring
- Interactive recording controls (Record, Stop, Play, Save)
- Action capture system for taps, swipes, text input, etc.
- Element identification with multiple locator strategies
- Integration with existing test case infrastructure
- Cross-platform support (iOS and Android)

Author: Mobile Automation Team
Version: 1.0.0
"""

__version__ = "1.0.0"
__author__ = "Mobile Automation Team"

# Import main recorder components
from .recorder_manager import RecorderManager
from .action_capture import ActionCapture
from .session_manager import RecordingSession

__all__ = [
    'RecorderManager',
    'ActionCapture', 
    'RecordingSession'
]
