"""
Recorder Manager Module

Handles the main recording functionality including session management,
device interaction capture, and integration with existing systems.
"""

import json
import os
import time
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from flask import session

from utils.id_generator import generate_action_id
from recorder.action_capture import ActionCapture
from recorder.session_manager import RecordingSession
from recorder.integration_utils import (
    DeviceIntegration, TestCaseIntegration,
    ExecutionIntegration, ScreenshotIntegration, SettingsIntegration
)

logger = logging.getLogger(__name__)


class RecorderManager:
    """Main recorder manager class that coordinates recording functionality"""
    
    def __init__(self):
        self.active_sessions: Dict[str, RecordingSession] = {}
        self.action_capture = ActionCapture()
        
    def start_recording_session(self, device_id: str, session_id: str) -> Dict[str, Any]:
        """
        Start a new recording session for the specified device

        Args:
            device_id: The device identifier
            session_id: The session identifier

        Returns:
            Dict containing session info and status
        """
        try:
            # Check if session already exists
            if session_id in self.active_sessions:
                return {
                    'success': False,
                    'error': 'Recording session already active'
                }

            # Validate device connection
            if not self._validate_device_connection(device_id):
                return {
                    'success': False,
                    'error': f'Device {device_id} is not connected or not accessible'
                }

            # Check for existing active sessions for this device
            existing_session = self._get_active_session_for_device(device_id)
            if existing_session:
                logger.warning(f"Device {device_id} already has active session {existing_session}")
                # Optionally cleanup the existing session
                self.cleanup_session(existing_session)

            # Create new recording session
            recording_session = RecordingSession(device_id, session_id)
            self.active_sessions[session_id] = recording_session

            # Initialize session with device capabilities
            self._initialize_session_capabilities(recording_session)

            logger.info(f"Started recording session {session_id} for device {device_id}")

            return {
                'success': True,
                'session_id': session_id,
                'device_id': device_id,
                'start_time': recording_session.start_time.isoformat(),
                'status': 'recording',
                'capabilities': recording_session.metadata.get('capabilities', {})
            }

        except Exception as e:
            logger.error(f"Error starting recording session: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def stop_recording_session(self, session_id: str) -> Dict[str, Any]:
        """
        Stop an active recording session
        
        Args:
            session_id: The session identifier
            
        Returns:
            Dict containing session info and captured actions
        """
        try:
            if session_id not in self.active_sessions:
                return {
                    'success': False,
                    'error': 'No active recording session found'
                }
            
            recording_session = self.active_sessions[session_id]
            recording_session.stop_recording()
            
            # Get captured actions
            actions = recording_session.get_captured_actions()
            
            logger.info(f"Stopped recording session {session_id}, captured {len(actions)} actions")
            
            return {
                'success': True,
                'session_id': session_id,
                'actions': actions,
                'duration': recording_session.get_duration(),
                'status': 'stopped'
            }
            
        except Exception as e:
            logger.error(f"Error stopping recording session: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def capture_action(self, session_id: str, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Capture a user action during recording

        Args:
            session_id: The session identifier
            action_data: The action data to capture

        Returns:
            Dict containing capture status and action info
        """
        try:
            if session_id not in self.active_sessions:
                return {
                    'success': False,
                    'error': 'No active recording session found'
                }

            recording_session = self.active_sessions[session_id]

            # Validate action data
            validation_result = self._validate_action_data(action_data)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'error': f"Invalid action data: {validation_result['error']}"
                }

            # Enhance action data with session context
            enhanced_action_data = self._enhance_action_data(action_data, recording_session)

            # Process the action through action capture system
            processed_action = self.action_capture.process_action(enhanced_action_data)

            # Validate processed action
            if not self._validate_processed_action(processed_action):
                return {
                    'success': False,
                    'error': 'Failed to process action properly'
                }

            # Add session-specific metadata
            processed_action['session_id'] = session_id
            processed_action['device_id'] = recording_session.device_id
            processed_action['sequence_number'] = len(recording_session.captured_actions) + 1

            # Add to recording session
            recording_session.add_action(processed_action)

            # Capture screenshot for visual validation
            self._capture_action_screenshot(processed_action, recording_session)

            logger.debug(f"Captured action {processed_action.get('action_id')} in session {session_id}")

            return {
                'success': True,
                'action_id': processed_action.get('action_id'),
                'action_type': processed_action.get('type'),
                'timestamp': processed_action.get('timestamp'),
                'sequence_number': processed_action.get('sequence_number')
            }

        except Exception as e:
            logger.error(f"Error capturing action: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def _validate_action_data(self, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate action data before processing

        Args:
            action_data: The action data to validate

        Returns:
            Dict containing validation result
        """
        try:
            required_fields = ['type', 'timestamp']

            for field in required_fields:
                if field not in action_data:
                    return {
                        'valid': False,
                        'error': f"Missing required field: {field}"
                    }

            # Validate action type
            action_type = action_data.get('type')
            if not isinstance(action_type, str) or not action_type.strip():
                return {
                    'valid': False,
                    'error': "Action type must be a non-empty string"
                }

            # Validate coordinates if present
            if 'x' in action_data or 'y' in action_data:
                x = action_data.get('x')
                y = action_data.get('y')

                if not isinstance(x, (int, float)) or not isinstance(y, (int, float)):
                    return {
                        'valid': False,
                        'error': "Coordinates must be numeric"
                    }

                if x < 0 or y < 0:
                    return {
                        'valid': False,
                        'error': "Coordinates must be non-negative"
                    }

            return {'valid': True}

        except Exception as e:
            return {
                'valid': False,
                'error': f"Validation error: {str(e)}"
            }

    def _enhance_action_data(self, action_data: Dict[str, Any],
                           recording_session: RecordingSession) -> Dict[str, Any]:
        """
        Enhance action data with session context and device information

        Args:
            action_data: The original action data
            recording_session: The recording session

        Returns:
            Enhanced action data
        """
        enhanced_data = action_data.copy()

        # Add session context
        enhanced_data['session_context'] = {
            'session_id': recording_session.session_id,
            'device_id': recording_session.device_id,
            'platform': recording_session.metadata.get('platform', 'ios'),
            'session_duration': recording_session.get_duration(),
            'action_count': len(recording_session.captured_actions)
        }

        # Add device capabilities
        capabilities = recording_session.metadata.get('capabilities', {})
        enhanced_data['device_capabilities'] = capabilities

        return enhanced_data

    def _validate_processed_action(self, processed_action: Dict[str, Any]) -> bool:
        """
        Validate that the processed action is properly formatted

        Args:
            processed_action: The processed action to validate

        Returns:
            True if valid, False otherwise
        """
        try:
            required_fields = ['action_id', 'type', 'timestamp']

            for field in required_fields:
                if field not in processed_action:
                    logger.error(f"Processed action missing required field: {field}")
                    return False

            # Validate action ID format
            action_id = processed_action.get('action_id')
            if not isinstance(action_id, str) or len(action_id) != 10:
                logger.error(f"Invalid action ID format: {action_id}")
                return False

            return True

        except Exception as e:
            logger.error(f"Error validating processed action: {str(e)}")
            return False

    def _capture_action_screenshot(self, processed_action: Dict[str, Any],
                                 recording_session: RecordingSession) -> None:
        """
        Capture screenshot for action visual validation

        Args:
            processed_action: The processed action
            recording_session: The recording session
        """
        try:
            # This would integrate with existing screenshot capture logic
            action_id = processed_action.get('action_id')
            logger.debug(f"Screenshot capture for action {action_id} would be implemented here")

        except Exception as e:
            logger.error(f"Error capturing action screenshot: {str(e)}")
    
    def save_test_case(self, session_id: str, test_case_name: str,
                      test_case_dir: str) -> Dict[str, Any]:
        """
        Save recorded session as a test case with comprehensive validation and optimization

        Args:
            session_id: The session identifier
            test_case_name: Name for the test case
            test_case_dir: Directory to save the test case

        Returns:
            Dict containing save status and file info
        """
        try:
            if session_id not in self.active_sessions:
                return {
                    'success': False,
                    'error': 'No recording session found'
                }

            recording_session = self.active_sessions[session_id]
            raw_actions = recording_session.get_captured_actions()

            if not raw_actions:
                return {
                    'success': False,
                    'error': 'No actions recorded'
                }

            # Validate test case name
            validation_result = self._validate_test_case_name(test_case_name, test_case_dir)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'error': validation_result['error']
                }

            # Optimize actions before saving
            optimized_actions = self.action_capture.optimize_actions(raw_actions)

            # Validate action sequence
            validation = self.action_capture.validate_action_sequence(optimized_actions)
            if not validation['valid']:
                logger.warning(f"Action sequence validation issues: {validation['issues']}")
                # Continue saving but include validation info

            # Generate comprehensive test case metadata
            session_info = recording_session.get_session_info()
            session_stats = self.get_session_statistics(session_id)

            # Create enhanced test case data structure
            test_case_data = {
                'name': test_case_name,
                'created': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'version': '1.0.0',
                'metadata': {
                    'device_id': recording_session.device_id,
                    'platform': recording_session.metadata.get('platform', 'ios'),
                    'session_duration': recording_session.get_duration(),
                    'total_actions': len(optimized_actions),
                    'original_actions_count': len(raw_actions),
                    'optimization_ratio': len(optimized_actions) / len(raw_actions) if raw_actions else 1,
                    'capabilities': recording_session.metadata.get('capabilities', {}),
                    'validation': validation
                },
                'session_info': session_info,
                'statistics': session_stats.get('success') and session_stats or {},
                'actions': optimized_actions
            }

            # Generate filename with proper sanitization
            sanitized_name = self._sanitize_filename(test_case_name)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{sanitized_name}_{timestamp}.json"
            filepath = os.path.join(test_case_dir, filename)

            # Ensure directory exists
            os.makedirs(test_case_dir, exist_ok=True)

            # Save to file with proper formatting
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(test_case_data, f, indent=2, ensure_ascii=False)

            # Save backup copy if enabled
            backup_result = self._save_backup_copy(test_case_data, filepath)

            # Update database if integration is available
            db_result = self._save_to_database(test_case_data, filepath)

            # Generate test case report
            report_result = self._generate_test_case_report(test_case_data, filepath)

            # Clean up session
            del self.active_sessions[session_id]

            logger.info(f"Saved test case '{test_case_name}' to {filepath}")

            result = {
                'success': True,
                'filename': filename,
                'filepath': filepath,
                'actions_count': len(optimized_actions),
                'original_actions_count': len(raw_actions),
                'test_case_name': test_case_name,
                'optimization_applied': len(optimized_actions) != len(raw_actions),
                'validation_warnings': validation.get('warnings', [])
            }

            # Add optional results
            if backup_result:
                result['backup_created'] = backup_result
            if db_result:
                result['database_saved'] = db_result
            if report_result:
                result['report_generated'] = report_result

            return result

        except Exception as e:
            logger.error(f"Error saving test case: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def _validate_test_case_name(self, test_case_name: str, test_case_dir: str) -> Dict[str, Any]:
        """
        Validate test case name for uniqueness and format

        Args:
            test_case_name: The proposed test case name
            test_case_dir: Directory where test case will be saved

        Returns:
            Dict containing validation result
        """
        try:
            # Check if name is empty or too short
            if not test_case_name or len(test_case_name.strip()) < 3:
                return {
                    'valid': False,
                    'error': 'Test case name must be at least 3 characters long'
                }

            # Check for invalid characters
            invalid_chars = ['<', '>', ':', '"', '|', '?', '*', '/', '\\']
            if any(char in test_case_name for char in invalid_chars):
                return {
                    'valid': False,
                    'error': f'Test case name contains invalid characters: {invalid_chars}'
                }

            # Check for uniqueness (basic check)
            sanitized_name = self._sanitize_filename(test_case_name)
            existing_files = []

            if os.path.exists(test_case_dir):
                for file in os.listdir(test_case_dir):
                    if file.startswith(sanitized_name) and file.endswith('.json'):
                        existing_files.append(file)

            if existing_files:
                logger.warning(f"Similar test case names found: {existing_files}")
                # Don't fail, just warn - timestamp will make it unique

            return {'valid': True}

        except Exception as e:
            return {
                'valid': False,
                'error': f'Validation error: {str(e)}'
            }

    def _sanitize_filename(self, filename: str) -> str:
        """
        Sanitize filename for safe file system usage

        Args:
            filename: The original filename

        Returns:
            Sanitized filename
        """
        # Replace spaces and special characters
        sanitized = filename.strip()
        sanitized = ''.join(c if c.isalnum() or c in '-_' else '_' for c in sanitized)

        # Limit length
        if len(sanitized) > 50:
            sanitized = sanitized[:50]

        # Ensure it doesn't start with a number or special character
        if sanitized and not sanitized[0].isalpha():
            sanitized = 'test_' + sanitized

        return sanitized or 'unnamed_test'

    def _save_backup_copy(self, test_case_data: Dict[str, Any], original_filepath: str) -> bool:
        """
        Save a backup copy of the test case

        Args:
            test_case_data: The test case data
            original_filepath: Path to the original file

        Returns:
            True if backup saved successfully, False otherwise
        """
        try:
            backup_dir = os.path.join(os.path.dirname(original_filepath), 'backups')
            os.makedirs(backup_dir, exist_ok=True)

            backup_filename = f"backup_{os.path.basename(original_filepath)}"
            backup_filepath = os.path.join(backup_dir, backup_filename)

            with open(backup_filepath, 'w', encoding='utf-8') as f:
                json.dump(test_case_data, f, indent=2, ensure_ascii=False)

            logger.debug(f"Backup saved to {backup_filepath}")
            return True

        except Exception as e:
            logger.error(f"Error saving backup: {str(e)}")
            return False

    def _save_to_database(self, test_case_data: Dict[str, Any], filepath: str) -> bool:
        """
        Save test case information to database

        Args:
            test_case_data: The test case data
            filepath: Path to the saved file

        Returns:
            True if saved to database successfully, False otherwise
        """
        try:
            # This would integrate with existing database logic
            # For now, just log the intent
            logger.debug(f"Database integration for test case {test_case_data['name']} would be implemented here")
            return True

        except Exception as e:
            logger.error(f"Error saving to database: {str(e)}")
            return False

    def _generate_test_case_report(self, test_case_data: Dict[str, Any], filepath: str) -> bool:
        """
        Generate a human-readable report for the test case

        Args:
            test_case_data: The test case data
            filepath: Path to the saved file

        Returns:
            True if report generated successfully, False otherwise
        """
        try:
            report_dir = os.path.join(os.path.dirname(filepath), 'reports')
            os.makedirs(report_dir, exist_ok=True)

            report_filename = f"report_{os.path.splitext(os.path.basename(filepath))[0]}.html"
            report_filepath = os.path.join(report_dir, report_filename)

            # Generate HTML report
            html_content = self._generate_html_report(test_case_data)

            with open(report_filepath, 'w', encoding='utf-8') as f:
                f.write(html_content)

            logger.debug(f"Report generated at {report_filepath}")
            return True

        except Exception as e:
            logger.error(f"Error generating report: {str(e)}")
            return False

    def _generate_html_report(self, test_case_data: Dict[str, Any]) -> str:
        """
        Generate HTML report content for the test case

        Args:
            test_case_data: The test case data

        Returns:
            HTML content string
        """
        try:
            actions = test_case_data.get('actions', [])
            metadata = test_case_data.get('metadata', {})

            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Test Case Report: {test_case_data['name']}</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .header {{ background: #f0f0f0; padding: 20px; border-radius: 5px; }}
                    .action {{ margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 3px; }}
                    .metadata {{ background: #f9f9f9; padding: 15px; margin: 20px 0; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>Test Case: {test_case_data['name']}</h1>
                    <p>Created: {test_case_data['created']}</p>
                    <p>Platform: {metadata.get('platform', 'Unknown')}</p>
                    <p>Total Actions: {len(actions)}</p>
                </div>

                <div class="metadata">
                    <h2>Metadata</h2>
                    <p>Device ID: {metadata.get('device_id', 'Unknown')}</p>
                    <p>Session Duration: {metadata.get('session_duration', 0):.2f} seconds</p>
                    <p>Optimization Ratio: {metadata.get('optimization_ratio', 1):.2f}</p>
                </div>

                <h2>Actions</h2>
            """

            for i, action in enumerate(actions, 1):
                action_type = action.get('type', 'unknown')
                action_id = action.get('action_id', 'N/A')

                html += f"""
                <div class="action">
                    <h3>Action {i}: {action_type}</h3>
                    <p>ID: {action_id}</p>
                """

                if 'x' in action and 'y' in action:
                    html += f"<p>Coordinates: ({action['x']}, {action['y']})</p>"

                if 'locator_type' in action and 'locator_value' in action:
                    html += f"<p>Locator: {action['locator_type']} = {action['locator_value']}</p>"

                html += "</div>"

            html += """
                </body>
            </html>
            """

            return html

        except Exception as e:
            logger.error(f"Error generating HTML report: {str(e)}")
            return f"<html><body><h1>Error generating report: {str(e)}</h1></body></html>"
    
    def get_session_status(self, session_id: str) -> Dict[str, Any]:
        """
        Get status of a recording session
        
        Args:
            session_id: The session identifier
            
        Returns:
            Dict containing session status info
        """
        if session_id not in self.active_sessions:
            return {
                'active': False,
                'error': 'Session not found'
            }
        
        recording_session = self.active_sessions[session_id]
        
        return {
            'active': True,
            'session_id': session_id,
            'device_id': recording_session.device_id,
            'start_time': recording_session.start_time,
            'duration': recording_session.get_duration(),
            'actions_count': len(recording_session.get_captured_actions()),
            'status': recording_session.status
        }
    
    def cleanup_session(self, session_id: str) -> bool:
        """
        Clean up a recording session

        Args:
            session_id: The session identifier

        Returns:
            True if cleanup successful, False otherwise
        """
        try:
            if session_id in self.active_sessions:
                recording_session = self.active_sessions[session_id]

                # Perform cleanup operations
                self._cleanup_session_resources(recording_session)

                del self.active_sessions[session_id]
                logger.info(f"Cleaned up recording session {session_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"Error cleaning up session: {str(e)}")
            return False

    def _validate_device_connection(self, device_id: str) -> bool:
        """
        Validate that the device is connected and accessible

        Args:
            device_id: The device identifier

        Returns:
            True if device is accessible, False otherwise
        """
        try:
            return DeviceIntegration.validate_device_connection(device_id)
        except Exception as e:
            logger.error(f"Error validating device connection: {str(e)}")
            return False

    def _get_active_session_for_device(self, device_id: str) -> Optional[str]:
        """
        Get active session ID for a device

        Args:
            device_id: The device identifier

        Returns:
            Session ID if found, None otherwise
        """
        for session_id, session in self.active_sessions.items():
            if session.device_id == device_id and session.status == 'recording':
                return session_id
        return None

    def _initialize_session_capabilities(self, recording_session: RecordingSession) -> None:
        """
        Initialize session with device-specific capabilities

        Args:
            recording_session: The recording session to initialize
        """
        try:
            # Detect platform and set capabilities
            platform = recording_session.metadata.get('platform', 'ios')

            if platform == 'ios':
                capabilities = {
                    'supported_locators': ['id', 'xpath', 'accessibility_id', 'class_name'],
                    'supported_actions': [
                        'tap', 'double_tap', 'long_press', 'swipe', 'text_input',
                        'scroll', 'pinch', 'rotate', 'home_button'
                    ],
                    'platform_features': ['wda_integration', 'ios_simulator_support']
                }
            else:
                capabilities = {
                    'supported_locators': ['id', 'xpath', 'uiselector', 'accessibility_id'],
                    'supported_actions': [
                        'tap', 'double_tap', 'long_press', 'swipe', 'text_input',
                        'scroll', 'pinch', 'rotate', 'back_button', 'home_button'
                    ],
                    'platform_features': ['uiautomator2_integration', 'webview_context_switching']
                }

            recording_session.metadata['capabilities'] = capabilities

        except Exception as e:
            logger.error(f"Error initializing session capabilities: {str(e)}")

    def _cleanup_session_resources(self, recording_session: RecordingSession) -> None:
        """
        Clean up resources associated with a recording session

        Args:
            recording_session: The recording session to clean up
        """
        try:
            # Clean up any temporary files, screenshots, etc.
            # This would integrate with existing cleanup logic
            logger.debug(f"Cleaning up resources for session {recording_session.session_id}")

        except Exception as e:
            logger.error(f"Error cleaning up session resources: {str(e)}")

    def get_session_statistics(self, session_id: str) -> Dict[str, Any]:
        """
        Get detailed statistics for a recording session

        Args:
            session_id: The session identifier

        Returns:
            Dict containing session statistics
        """
        try:
            if session_id not in self.active_sessions:
                return {
                    'success': False,
                    'error': 'Session not found'
                }

            recording_session = self.active_sessions[session_id]
            actions = recording_session.get_captured_actions()

            # Calculate statistics
            action_types = {}
            locator_types = {}
            total_duration = 0

            for action in actions:
                action_type = action.get('type', 'unknown')
                action_types[action_type] = action_types.get(action_type, 0) + 1

                locator_type = action.get('locator_type')
                if locator_type:
                    locator_types[locator_type] = locator_types.get(locator_type, 0) + 1

                if 'duration' in action:
                    total_duration += action.get('duration', 0)

            return {
                'success': True,
                'session_id': session_id,
                'total_actions': len(actions),
                'action_types': action_types,
                'locator_types': locator_types,
                'total_duration': total_duration,
                'session_duration': recording_session.get_duration(),
                'actions_per_minute': len(actions) / max(recording_session.get_duration() / 60, 1)
            }

        except Exception as e:
            logger.error(f"Error getting session statistics: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
