"""
Playback Manager Module

Handles playback of recorded test cases with integration to existing test execution engine.
Provides both full test case execution and individual action step execution for debugging.
"""

import json
import logging
import time
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime

logger = logging.getLogger(__name__)


class PlaybackManager:
    """Manages playback of recorded test cases"""
    
    def __init__(self):
        self.active_playbacks: Dict[str, 'PlaybackSession'] = {}
        self.execution_callbacks: Dict[str, Callable] = {}
        
    def register_execution_callback(self, action_type: str, callback: Callable) -> None:
        """
        Register a callback function for executing specific action types
        
        Args:
            action_type: The action type to handle
            callback: Function to call for execution
        """
        self.execution_callbacks[action_type] = callback
        logger.debug(f"Registered execution callback for action type: {action_type}")
    
    def start_playback(self, playback_id: str, test_case_data: Dict[str, Any], 
                      device_id: str, options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Start playback of a recorded test case
        
        Args:
            playback_id: Unique identifier for this playback session
            test_case_data: The test case data to execute
            device_id: Target device for execution
            options: Optional playback configuration
            
        Returns:
            Dict containing playback status and info
        """
        try:
            if playback_id in self.active_playbacks:
                return {
                    'success': False,
                    'error': 'Playback session already active'
                }
            
            # Validate test case data
            validation_result = self._validate_test_case_data(test_case_data)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'error': f"Invalid test case data: {validation_result['error']}"
                }
            
            # Create playback session
            playback_session = PlaybackSession(
                playback_id=playback_id,
                test_case_data=test_case_data,
                device_id=device_id,
                options=options or {}
            )
            
            self.active_playbacks[playback_id] = playback_session
            
            logger.info(f"Started playback session {playback_id} for test case '{test_case_data.get('name')}'")
            
            return {
                'success': True,
                'playback_id': playback_id,
                'test_case_name': test_case_data.get('name'),
                'total_actions': len(test_case_data.get('actions', [])),
                'device_id': device_id,
                'status': 'ready'
            }
            
        except Exception as e:
            logger.error(f"Error starting playback: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def execute_playback(self, playback_id: str) -> Dict[str, Any]:
        """
        Execute the full playback session
        
        Args:
            playback_id: The playback session identifier
            
        Returns:
            Dict containing execution results
        """
        try:
            if playback_id not in self.active_playbacks:
                return {
                    'success': False,
                    'error': 'Playback session not found'
                }
            
            playback_session = self.active_playbacks[playback_id]
            actions = playback_session.test_case_data.get('actions', [])
            
            if not actions:
                return {
                    'success': False,
                    'error': 'No actions to execute'
                }
            
            playback_session.start_execution()
            
            results = []
            failed_actions = []
            
            for i, action in enumerate(actions):
                try:
                    # Update current action
                    playback_session.set_current_action(i, action)
                    
                    # Execute action
                    action_result = self._execute_action(action, playback_session)
                    
                    results.append({
                        'action_index': i,
                        'action_id': action.get('action_id'),
                        'action_type': action.get('type'),
                        'success': action_result['success'],
                        'execution_time': action_result.get('execution_time', 0),
                        'error': action_result.get('error')
                    })
                    
                    if not action_result['success']:
                        failed_actions.append(i)
                        
                        # Check if we should continue on failure
                        if not playback_session.options.get('continue_on_failure', True):
                            logger.warning(f"Stopping playback due to failed action {i}")
                            break
                    
                    # Add delay between actions if specified
                    delay = playback_session.options.get('action_delay', 0.5)
                    if delay > 0:
                        time.sleep(delay)
                        
                except Exception as e:
                    logger.error(f"Error executing action {i}: {str(e)}")
                    results.append({
                        'action_index': i,
                        'action_id': action.get('action_id'),
                        'action_type': action.get('type'),
                        'success': False,
                        'error': str(e)
                    })
                    failed_actions.append(i)
            
            playback_session.complete_execution()
            
            # Calculate summary statistics
            total_actions = len(results)
            successful_actions = sum(1 for r in results if r['success'])
            total_time = playback_session.get_execution_duration()
            
            execution_summary = {
                'success': len(failed_actions) == 0,
                'playback_id': playback_id,
                'total_actions': total_actions,
                'successful_actions': successful_actions,
                'failed_actions': len(failed_actions),
                'success_rate': successful_actions / total_actions if total_actions > 0 else 0,
                'execution_time': total_time,
                'failed_action_indices': failed_actions,
                'detailed_results': results
            }
            
            logger.info(f"Playback {playback_id} completed: {successful_actions}/{total_actions} actions successful")
            
            return execution_summary
            
        except Exception as e:
            logger.error(f"Error executing playback: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def execute_single_action(self, playback_id: str, action_index: int) -> Dict[str, Any]:
        """
        Execute a single action from the test case for debugging
        
        Args:
            playback_id: The playback session identifier
            action_index: Index of the action to execute
            
        Returns:
            Dict containing execution result
        """
        try:
            if playback_id not in self.active_playbacks:
                return {
                    'success': False,
                    'error': 'Playback session not found'
                }
            
            playback_session = self.active_playbacks[playback_id]
            actions = playback_session.test_case_data.get('actions', [])
            
            if action_index < 0 or action_index >= len(actions):
                return {
                    'success': False,
                    'error': f'Invalid action index: {action_index}'
                }
            
            action = actions[action_index]
            playback_session.set_current_action(action_index, action)
            
            # Execute the single action
            result = self._execute_action(action, playback_session)
            
            logger.info(f"Single action execution {action_index}: {result['success']}")
            
            return {
                'success': result['success'],
                'action_index': action_index,
                'action_id': action.get('action_id'),
                'action_type': action.get('type'),
                'execution_time': result.get('execution_time', 0),
                'error': result.get('error')
            }
            
        except Exception as e:
            logger.error(f"Error executing single action: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _execute_action(self, action: Dict[str, Any], playback_session: 'PlaybackSession') -> Dict[str, Any]:
        """
        Execute a single action using registered callbacks or default execution
        
        Args:
            action: The action to execute
            playback_session: The playback session context
            
        Returns:
            Dict containing execution result
        """
        start_time = time.time()
        
        try:
            action_type = action.get('type')
            
            if action_type in self.execution_callbacks:
                # Use registered callback
                callback = self.execution_callbacks[action_type]
                result = callback(action, playback_session)
            else:
                # Use default execution (integrate with existing test execution engine)
                result = self._default_action_execution(action, playback_session)
            
            execution_time = time.time() - start_time
            
            if isinstance(result, dict):
                result['execution_time'] = execution_time
                return result
            else:
                return {
                    'success': bool(result),
                    'execution_time': execution_time
                }
                
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Error executing action {action.get('action_id')}: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'execution_time': execution_time
            }
    
    def _default_action_execution(self, action: Dict[str, Any], 
                                playback_session: 'PlaybackSession') -> Dict[str, Any]:
        """
        Default action execution that integrates with existing test execution engine
        
        Args:
            action: The action to execute
            playback_session: The playback session context
            
        Returns:
            Dict containing execution result
        """
        try:
            # This would integrate with the existing test execution engine
            # For now, simulate execution
            action_type = action.get('type')
            
            logger.debug(f"Executing {action_type} action: {action.get('action_id')}")
            
            # Simulate execution delay
            time.sleep(0.1)
            
            # Return success for simulation
            return {
                'success': True,
                'message': f"Simulated execution of {action_type} action"
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _validate_test_case_data(self, test_case_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate test case data before playback
        
        Args:
            test_case_data: The test case data to validate
            
        Returns:
            Dict containing validation result
        """
        try:
            if not isinstance(test_case_data, dict):
                return {
                    'valid': False,
                    'error': 'Test case data must be a dictionary'
                }
            
            if 'actions' not in test_case_data:
                return {
                    'valid': False,
                    'error': 'Test case data must contain actions'
                }
            
            actions = test_case_data['actions']
            if not isinstance(actions, list):
                return {
                    'valid': False,
                    'error': 'Actions must be a list'
                }
            
            if len(actions) == 0:
                return {
                    'valid': False,
                    'error': 'Test case must contain at least one action'
                }
            
            # Validate each action
            for i, action in enumerate(actions):
                if not isinstance(action, dict):
                    return {
                        'valid': False,
                        'error': f'Action {i} must be a dictionary'
                    }
                
                if 'type' not in action:
                    return {
                        'valid': False,
                        'error': f'Action {i} must have a type'
                    }
            
            return {'valid': True}
            
        except Exception as e:
            return {
                'valid': False,
                'error': f'Validation error: {str(e)}'
            }
    
    def stop_playback(self, playback_id: str) -> Dict[str, Any]:
        """
        Stop an active playback session
        
        Args:
            playback_id: The playback session identifier
            
        Returns:
            Dict containing stop result
        """
        try:
            if playback_id not in self.active_playbacks:
                return {
                    'success': False,
                    'error': 'Playback session not found'
                }
            
            playback_session = self.active_playbacks[playback_id]
            playback_session.stop_execution()
            
            del self.active_playbacks[playback_id]
            
            logger.info(f"Stopped playback session {playback_id}")
            
            return {
                'success': True,
                'playback_id': playback_id
            }
            
        except Exception as e:
            logger.error(f"Error stopping playback: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_playback_status(self, playback_id: str) -> Dict[str, Any]:
        """
        Get status of a playback session
        
        Args:
            playback_id: The playback session identifier
            
        Returns:
            Dict containing playback status
        """
        try:
            if playback_id not in self.active_playbacks:
                return {
                    'active': False,
                    'error': 'Playback session not found'
                }
            
            playback_session = self.active_playbacks[playback_id]
            
            return {
                'active': True,
                'playback_id': playback_id,
                'test_case_name': playback_session.test_case_data.get('name'),
                'device_id': playback_session.device_id,
                'status': playback_session.status,
                'current_action_index': playback_session.current_action_index,
                'total_actions': len(playback_session.test_case_data.get('actions', [])),
                'execution_time': playback_session.get_execution_duration()
            }
            
        except Exception as e:
            logger.error(f"Error getting playback status: {str(e)}")
            return {
                'active': False,
                'error': str(e)
            }


class PlaybackSession:
    """Represents an individual playback session"""
    
    def __init__(self, playback_id: str, test_case_data: Dict[str, Any], 
                 device_id: str, options: Dict[str, Any]):
        self.playback_id = playback_id
        self.test_case_data = test_case_data
        self.device_id = device_id
        self.options = options
        self.status = 'ready'
        self.start_time = None
        self.end_time = None
        self.current_action_index = -1
        self.current_action = None
        
    def start_execution(self) -> None:
        """Start the execution"""
        self.status = 'executing'
        self.start_time = datetime.now()
        
    def complete_execution(self) -> None:
        """Complete the execution"""
        self.status = 'completed'
        self.end_time = datetime.now()
        
    def stop_execution(self) -> None:
        """Stop the execution"""
        self.status = 'stopped'
        self.end_time = datetime.now()
        
    def set_current_action(self, index: int, action: Dict[str, Any]) -> None:
        """Set the current action being executed"""
        self.current_action_index = index
        self.current_action = action
        
    def get_execution_duration(self) -> float:
        """Get execution duration in seconds"""
        if not self.start_time:
            return 0.0
        
        end_time = self.end_time or datetime.now()
        duration = (end_time - self.start_time).total_seconds()
        return round(duration, 2)
