import os
import time
import logging
from appium import webdriver
from appium.options.ios import XCUITestOptions
from appium.options.android import UiAutomator2Options
from appium.webdriver.common.appiumby import AppiumBy
import base64
import io
from PIL import Image


class BrowserStackDeviceController:
    """
    BrowserStack-specific device controller for cloud device connections
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.driver = None
        self.device_id = None
        self._platform_name = None
        self.device_name = None
        self.os_version = None
        self.app_id = None
        self.username = None
        self.access_key = None
        self.is_connected = False
        self.session_id = None
        
    def connect_to_device(self, username, access_key, app_id, device_name=None, os_version=None, platform=None):
        """
        Connect to a BrowserStack device
        
        Args:
            username (str): BrowserStack username
            access_key (str): BrowserStack access key
            app_id (str): BrowserStack app ID
            device_name (str, optional): Device name for specific device selection
            os_version (str, optional): OS version for specific device selection
            platform (str, optional): Platform hint ('ios' or 'android')
            
        Returns:
            bool: True if connection successful, False otherwise
        """
        self.logger.info(f"Connecting to BrowserStack device with app ID: {app_id}")
        
        if not all([username, access_key, app_id]):
            self.logger.error("Missing required BrowserStack credentials or app ID")
            return False
            
        self.username = username
        self.access_key = access_key
        self.app_id = app_id
        self.device_name = device_name
        self.os_version = os_version
        self._platform_name = platform
        
        # Generate a unique device ID for BrowserStack sessions
        self.device_id = f"browserstack_{int(time.time())}"
        
        try:
            # Determine platform if not provided
            if not platform:
                # Default to iOS if not specified
                platform = 'ios'

            # Store platform name for later use
            self._platform_name = platform
            
            # Common capabilities
            common_caps = {
                'browserstack.user': username,
                'browserstack.key': access_key,
                'project': 'Mobile App AutoTest',
                'build': f'{platform.capitalize()} BrowserStack Test',
                'name': f'{platform.capitalize()} Cloud Device Test',
                'app': app_id,
                'autoGrantPermissions': True,
                'browserstack.debug': True,
                'browserstack.networkLogs': True,
                'browserstack.deviceLogs': True,
                'newCommandTimeout': 300
            }
            
            # Add device-specific capabilities if provided
            if device_name:
                common_caps['device'] = device_name
            if os_version:
                common_caps['os_version'] = os_version
                
            # Platform-specific setup
            if platform.lower() == 'ios':
                options = XCUITestOptions()
                options.platform_name = 'iOS'
                options.automation_name = 'XCUITest'
                
                # Set device name and OS version if provided
                if device_name:
                    options.device_name = device_name
                else:
                    options.device_name = 'iPhone 14'  # Default device
                    
                if os_version:
                    options.platform_version = os_version
                else:
                    options.platform_version = '16'  # Default version
                    
                # Set all capabilities
                for key, value in common_caps.items():
                    options.set_capability(key, value)
                    
            elif platform.lower() == 'android':
                options = UiAutomator2Options()
                options.platform_name = 'Android'
                options.automation_name = 'UiAutomator2'
                
                # Set device name and OS version if provided
                if device_name:
                    options.device_name = device_name
                else:
                    options.device_name = 'Google Pixel 7'  # Default device
                    
                if os_version:
                    options.platform_version = os_version
                else:
                    options.platform_version = '13.0'  # Default version
                    
                # Set all capabilities
                for key, value in common_caps.items():
                    options.set_capability(key, value)
            else:
                self.logger.error(f"Unsupported platform: {platform}")
                return False
                
            # Create the driver connection
            hub_url = f'https://{username}:{access_key}@hub-cloud.browserstack.com/wd/hub'
            self.logger.info(f"Connecting to BrowserStack hub: {hub_url}")
            
            self.driver = webdriver.Remote(
                command_executor=hub_url,
                options=options
            )
            
            # Get session ID
            self.session_id = self.driver.session_id
            self.logger.info(f"Successfully connected to BrowserStack device. Session ID: {self.session_id}")
            
            # Wait for app to load
            time.sleep(5)

            self.is_connected = True

            # Set up Airtest integration for image-based actions
            self.setup_airtest_integration()

            return True
            
        except Exception as e:
            self.logger.error(f"Failed to connect to BrowserStack device: {str(e)}")
            if self.driver:
                try:
                    self.driver.quit()
                except:
                    pass
                self.driver = None
            return False
    
    def disconnect(self):
        """Disconnect from the BrowserStack device"""
        try:
            if self.driver:
                # Mark test session status before quitting
                try:
                    self.driver.execute_script('browserstack_executor: {"action": "setSessionStatus", "arguments": {"status":"completed"}}')
                except:
                    pass
                    
                self.driver.quit()
                self.driver = None
                
            self.is_connected = False
            self.session_id = None
            self.logger.info("Disconnected from BrowserStack device")
            return True
        except Exception as e:
            self.logger.error(f"Error disconnecting from BrowserStack device: {str(e)}")
            return False
    
    def is_session_active(self):
        """Check if the BrowserStack session is still active"""
        try:
            if not self.driver:
                return False
            # Try to get current activity/page source as a connectivity test
            self.driver.current_activity if self._platform_name.lower() == 'android' else self.driver.page_source
            return True
        except Exception:
            return False
    
    def get_screenshot(self):
        """Get screenshot from BrowserStack device"""
        try:
            if not self.driver:
                self.logger.error("No active BrowserStack session")
                return None
                
            # Get screenshot as base64
            screenshot_base64 = self.driver.get_screenshot_as_base64()
            
            # Convert to PIL Image
            screenshot_data = base64.b64decode(screenshot_base64)
            image = Image.open(io.BytesIO(screenshot_data))
            
            return image
        except Exception as e:
            self.logger.error(f"Failed to get screenshot from BrowserStack device: {str(e)}")
            return None
    
    def get_device_info(self):
        """Get device information"""
        return {
            'id': self.device_id,
            'name': self.device_name or f"BrowserStack {self._platform_name.capitalize()} Device",
            'platform': self._platform_name,
            'osVersion': self.os_version or 'Unknown',
            'type': 'cloud',
            'provider': 'browserstack',
            'session_id': self.session_id,
            'app_id': self.app_id
        }
    
    def execute_action(self, action_type, **kwargs):
        """Execute an action on the BrowserStack device"""
        try:
            if not self.driver:
                raise Exception("No active BrowserStack session")
                
            # This method can be extended to handle specific actions
            # For now, it provides access to the driver for action execution
            return True
        except Exception as e:
            self.logger.error(f"Failed to execute action on BrowserStack device: {str(e)}")
            return False
    
    def get_driver(self):
        """Get the Appium driver instance"""
        return self.driver

    # Make BrowserStack controller compatible with existing action framework
    # by exposing the same interface as regular device controllers

    @property
    def airtest_device(self):
        """For compatibility with existing actions that check for airtest_device"""
        return None  # BrowserStack doesn't use Airtest directly

    @property
    def device_dimensions(self):
        """Get device dimensions for coordinate scaling"""
        if self.driver:
            try:
                window_size = self.driver.get_window_size()
                return {
                    'width': window_size.get('width'),
                    'height': window_size.get('height')
                }
            except Exception as e:
                self.logger.warning(f"Could not get device dimensions: {e}")
        return None

    @property
    def platform_name(self):
        """Get platform name for compatibility"""
        return getattr(self, '_platform_name', 'Unknown')

    def setup_airtest_integration(self):
        """
        Set up Airtest integration for BrowserStack device
        Since BrowserStack provides a standard Appium driver, we don't need special handling
        """
        try:
            # BrowserStack driver is a standard Appium driver, so existing Airtest integration should work
            # We just need to make sure the driver is accessible
            if self.driver and self.session_id:
                self.logger.info(f"BrowserStack driver ready for Airtest integration. Session ID: {self.session_id}")
                return True
            else:
                self.logger.warning("Cannot setup Airtest - no active BrowserStack session")
                return False

        except Exception as e:
            self.logger.error(f"Failed to setup Airtest integration: {e}")
            return False

    def take_screenshot(self, filename=None, save_debug=False):
        """
        Take screenshot compatible with existing action framework

        Args:
            filename (str, optional): Path to save screenshot
            save_debug (bool): Whether to save debug images

        Returns:
            dict: Result with status and path
        """
        try:
            if not self.driver:
                return {"status": "error", "message": "No active BrowserStack session"}

            # Get screenshot as PIL Image
            screenshot_image = self.get_screenshot()
            if not screenshot_image:
                return {"status": "error", "message": "Failed to get screenshot from BrowserStack"}

            # If no filename provided, create a default one
            if not filename:
                import time
                timestamp = int(time.time() * 1000)
                filename = f"browserstack_screenshot_{timestamp}.png"

            # Ensure directory exists
            import os
            os.makedirs(os.path.dirname(filename), exist_ok=True)

            # Save the screenshot
            screenshot_image.save(filename)

            self.logger.info(f"Screenshot saved to: {filename}")
            return {
                "status": "success",
                "path": filename,
                "message": f"Screenshot saved to {filename}"
            }

        except Exception as e:
            self.logger.error(f"Error taking screenshot: {e}")
            return {"status": "error", "message": str(e)}

    def tap(self, x, y):
        """
        Tap at coordinates - compatible with existing action framework

        Args:
            x (int): X coordinate
            y (int): Y coordinate

        Returns:
            bool: True if successful
        """
        try:
            if not self.driver:
                raise Exception("No active BrowserStack session")

            self.driver.tap([(int(x), int(y))])
            self.logger.info(f"Tapped at ({x}, {y}) on BrowserStack device")
            return True

        except Exception as e:
            self.logger.error(f"Error tapping on BrowserStack device: {e}")
            return False

    def input_text(self, text):
        """
        Input text using standard Appium methods - compatible with existing framework

        Args:
            text (str): Text to input

        Returns:
            dict: Result with status and message
        """
        try:
            if not self.driver:
                return {"status": "error", "message": "No active BrowserStack session"}

            # For BrowserStack, we use standard Appium text input
            # The currently focused element will receive the text
            from selenium.webdriver.common.action_chains import ActionChains

            # Try using ActionChains to send text to the currently focused element
            actions = ActionChains(self.driver)
            actions.send_keys(text)
            actions.perform()

            self.logger.info(f"Input text successful: '{text}'")
            return {"status": "success", "message": f"Text input successful: '{text}'"}

        except Exception as e:
            self.logger.error(f"Error inputting text on BrowserStack device: {e}")
            return {"status": "error", "message": str(e)}

    def launch_app(self, bundle_id):
        """
        Launch app - for BrowserStack, the app is already launched during connection

        Args:
            bundle_id (str): App bundle ID

        Returns:
            bool: True if successful, False otherwise (for compatibility with restart_app_action)
        """
        try:
            if not self.driver:
                self.logger.error("No active BrowserStack session")
                return False

            # For BrowserStack, we can use the activate_app command
            self.driver.activate_app(bundle_id)

            self.logger.info(f"App launched/activated: {bundle_id}")
            return True

        except Exception as e:
            self.logger.error(f"Error launching app on BrowserStack device: {e}")
            return False

    def terminate_app(self, bundle_id):
        """
        Terminate app

        Args:
            bundle_id (str): App bundle ID

        Returns:
            bool: True if successful, False otherwise (for compatibility with restart_app_action)
        """
        try:
            if not self.driver:
                self.logger.error("No active BrowserStack session")
                return False

            # Use standard Appium terminate_app command
            self.driver.terminate_app(bundle_id)

            self.logger.info(f"App terminated: {bundle_id}")
            return True

        except Exception as e:
            self.logger.error(f"Error terminating app on BrowserStack device: {e}")
            return False

    def restart_app(self, bundle_id):
        """
        Restart app by terminating and launching it

        Args:
            bundle_id (str): App bundle ID

        Returns:
            dict: Result with status and message
        """
        try:
            if not self.driver:
                return {"status": "error", "message": "No active BrowserStack session"}

            # For BrowserStack, we can use the app that was already uploaded
            # First terminate the app if it's running
            try:
                self.driver.terminate_app(bundle_id)
                self.logger.info(f"Terminated app: {bundle_id}")
            except Exception as e:
                self.logger.warning(f"Could not terminate app (may not be running): {e}")

            # Wait a moment
            import time
            time.sleep(2)

            # Launch the app again
            self.driver.activate_app(bundle_id)

            self.logger.info(f"App restarted: {bundle_id}")
            return {"status": "success", "message": f"App restarted: {bundle_id}"}

        except Exception as e:
            self.logger.error(f"Error restarting app on BrowserStack device: {e}")
            return {"status": "error", "message": str(e)}

    def tap_element(self, locator_type, locator_value, timeout=10, interval=0.5):
        """
        Tap element by locator - required by existing tap actions

        Args:
            locator_type (str): Type of locator (xpath, accessibility_id, etc.)
            locator_value (str): Locator value
            timeout (int): Timeout in seconds
            interval (float): Retry interval

        Returns:
            dict: Result with status and message
        """
        try:
            if not self.driver:
                return {"status": "error", "message": "No active BrowserStack session"}

            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.common.exceptions import TimeoutException, NoSuchElementException

            # Map locator types to Selenium By types
            locator_map = {
                'xpath': By.XPATH,
                'id': By.ID,
                'name': By.NAME,
                'class_name': By.CLASS_NAME,
                'tag_name': By.TAG_NAME,
                'css_selector': By.CSS_SELECTOR,
                'accessibility_id': 'accessibility_id'  # Special case for Appium
            }

            by_type = locator_map.get(locator_type.lower())
            if not by_type:
                return {"status": "error", "message": f"Unsupported locator type: {locator_type}"}

            self.logger.info(f"Looking for element: {locator_type}={locator_value} with timeout={timeout}s")

            # Wait for element and tap
            wait = WebDriverWait(self.driver, timeout)

            try:
                if by_type == 'accessibility_id':
                    # Use modern Appium method for accessibility ID
                    from appium.webdriver.common.appiumby import AppiumBy
                    element = wait.until(EC.element_to_be_clickable((AppiumBy.ACCESSIBILITY_ID, locator_value)))
                else:
                    element = wait.until(EC.element_to_be_clickable((by_type, locator_value)))

                # Tap the element
                element.click()

                self.logger.info(f"Successfully tapped element: {locator_type}={locator_value}")
                return {"status": "success", "message": f"Tapped element: {locator_type}={locator_value}"}

            except TimeoutException:
                return {"status": "error", "message": f"Element not found within {timeout}s: {locator_type}={locator_value}"}
            except NoSuchElementException:
                return {"status": "error", "message": f"Element not found: {locator_type}={locator_value}"}

        except Exception as e:
            self.logger.error(f"Error tapping element on BrowserStack device: {e}")
            return {"status": "error", "message": str(e)}

    def find_element(self, locator_type, locator_value, timeout=10):
        """
        Find element by locator - required by exists actions

        Args:
            locator_type (str): Type of locator
            locator_value (str): Locator value
            timeout (int): Timeout in seconds

        Returns:
            WebElement or None
        """
        try:
            if not self.driver:
                return None

            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.common.exceptions import TimeoutException

            # Map locator types
            locator_map = {
                'xpath': By.XPATH,
                'id': By.ID,
                'name': By.NAME,
                'class_name': By.CLASS_NAME,
                'tag_name': By.TAG_NAME,
                'css_selector': By.CSS_SELECTOR,
                'accessibility_id': 'accessibility_id'
            }

            by_type = locator_map.get(locator_type.lower())
            if not by_type:
                return None

            wait = WebDriverWait(self.driver, timeout)

            try:
                if by_type == 'accessibility_id':
                    # Use modern Appium method for accessibility ID
                    from appium.webdriver.common.appiumby import AppiumBy
                    element = wait.until(EC.presence_of_element_located((AppiumBy.ACCESSIBILITY_ID, locator_value)))
                else:
                    element = wait.until(EC.presence_of_element_located((by_type, locator_value)))

                return element

            except TimeoutException:
                return None

        except Exception as e:
            self.logger.error(f"Error finding element: {e}")
            return None

    def send_keys(self, text):
        """
        Send keys to currently focused element - required by text input actions

        Args:
            text (str): Text to send

        Returns:
            dict: Result with status and message
        """
        try:
            if not self.driver:
                return {"status": "error", "message": "No active BrowserStack session"}

            # Get the currently focused element and send keys to it
            try:
                # Try to get the active element
                active_element = self.driver.switch_to.active_element
                if active_element:
                    active_element.send_keys(text)
                    self.logger.info(f"Sent keys to active element: '{text}'")
                    return {"status": "success", "message": f"Text input successful: '{text}'"}
                else:
                    # Fallback: use ActionChains
                    from selenium.webdriver.common.action_chains import ActionChains
                    actions = ActionChains(self.driver)
                    actions.send_keys(text)
                    actions.perform()
                    self.logger.info(f"Sent keys using ActionChains: '{text}'")
                    return {"status": "success", "message": f"Text input successful: '{text}'"}

            except Exception as e:
                self.logger.warning(f"Could not send keys to active element: {e}")
                # Fallback: use ActionChains
                from selenium.webdriver.common.action_chains import ActionChains
                actions = ActionChains(self.driver)
                actions.send_keys(text)
                actions.perform()
                self.logger.info(f"Sent keys using ActionChains fallback: '{text}'")
                return {"status": "success", "message": f"Text input successful: '{text}'"}

        except Exception as e:
            self.logger.error(f"Error sending keys on BrowserStack device: {e}")
            return {"status": "error", "message": str(e)}
