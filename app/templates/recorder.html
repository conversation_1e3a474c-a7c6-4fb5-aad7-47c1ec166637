<!-- Recorder Tab Content -->
<div class="tab-pane fade" id="recorder-tab" role="tabpanel" aria-labelledby="recorder-tab-btn">
    <div class="row mt-3">
        <!-- Left column for device screen and recording controls -->
        <div class="col-md-6">
            <!-- Recording Controls Card -->
            <div class="card mb-3">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Recording Controls</h5>
                    <div class="recording-status">
                        <span id="recordingStatusIndicator" class="badge bg-secondary">Not Recording</span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="d-flex gap-2 mb-3">
                        <button id="startRecordingBtn" class="btn btn-success" disabled>
                            <i class="bi bi-record-circle"></i> Record
                        </button>
                        <button id="stopRecordingBtn" class="btn btn-danger" disabled>
                            <i class="bi bi-stop-circle"></i> Stop
                        </button>
                        <button id="playRecordingBtn" class="btn btn-primary" disabled>
                            <i class="bi bi-play-circle"></i> Play All
                        </button>
                        <button id="saveRecordingBtn" class="btn btn-warning" disabled>
                            <i class="bi bi-save"></i> Save
                        </button>
                    </div>
                    
                    <!-- Recording Info -->
                    <div class="recording-info">
                        <div class="row">
                            <div class="col-6">
                                <small class="text-muted">Actions Recorded:</small>
                                <div id="actionsCount" class="fw-bold">0</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">Recording Time:</small>
                                <div id="recordingTime" class="fw-bold">00:00</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Instructions -->
                    <div class="alert alert-info mt-3 mb-0">
                        <small>
                            <i class="bi bi-info-circle"></i>
                            <strong>Instructions:</strong><br>
                            1. Connect to a device first<br>
                            2. Click "Record" to start capturing interactions<br>
                            3. Interact with the device screen below<br>
                            4. Click "Stop" when finished<br>
                            5. Use "Play All" to test your recording<br>
                            6. Click "Save" to create a test case
                        </small>
                    </div>
                </div>
            </div>

            <!-- Device Connection Card (Reuse from main tab) -->
            <div class="card mb-3">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Device Connection</h5>
                    <div class="d-flex align-items-center">
                        <div class="session-status-container me-2">
                            <div id="recorderSessionStatusIndicator" class="session-status-indicator" title="Appium Session Status">
                                <div class="status-circle status-disconnected">
                                    <div class="status-pulse"></div>
                                </div>
                            </div>
                            <small id="recorderSessionStatusText" class="text-muted ms-1">Disconnected</small>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Physical Device Connection Only -->
                    <div class="input-group mb-3">
                        <select id="recorderDeviceSelector" class="form-select" aria-label="Device selection">
                            <option selected disabled>Select a device</option>
                        </select>
                        <button id="recorderRefreshDevices" class="btn btn-outline-secondary" type="button">
                            <i class="bi bi-arrow-repeat"></i>
                        </button>
                        <button id="recorderConnectButton" class="btn btn-primary" type="button">
                            <i class="bi bi-arrow-repeat"></i> Connect
                        </button>
                    </div>
                </div>
            </div>

            <!-- Device Screen Card -->
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <div class="device-screen-header">
                        <h5 class="device-screen-title">Device Screen (Interactive)</h5>
                        <div class="device-screen-actions">
                            <button id="recorderRefreshScreenBtn" class="btn btn-sm" disabled>
                                <i class="bi bi-arrow-clockwise"></i> Refresh
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body position-relative" style="min-height: 400px;">
                    <div id="recorderLoadingOverlay" class="position-absolute top-0 start-0 end-0 bottom-0 bg-dark d-none" style="opacity: 0.7; z-index: 100;">
                        <div class="d-flex align-items-center justify-content-center h-100">
                            <div class="text-center">
                                <div class="spinner-border text-light mb-2" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="text-light" id="recorderLoadingMessage">Loading...</p>
                            </div>
                        </div>
                    </div>
                    <div id="recorderDeviceScreenContainer" class="position-relative text-center">
                        <img id="recorderDeviceScreen" src="static/img/no_device.png" class="img-fluid device-screen" alt="Device Screen">
                        <canvas id="recorderOverlayCanvas" class="position-absolute top-0 start-0 d-none"></canvas>
                        <!-- Recording overlay for visual feedback -->
                        <div id="recordingOverlay" class="position-absolute top-0 start-0 w-100 h-100 d-none" style="pointer-events: none; z-index: 10;">
                            <div class="recording-pulse"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right column for recorded actions list -->
        <div class="col-md-6">
            <!-- Recorded Actions Card -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Recorded Actions</h5>
                    <div class="btn-group">
                        <button class="btn btn-sm btn-outline-secondary" id="clearRecordedActions" disabled>
                            <i class="bi bi-trash"></i> Clear
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div id="recordedActionsList" class="recorded-actions-list">
                        <div class="text-center text-muted p-4">
                            <i class="bi bi-record-circle display-4"></i>
                            <p class="mt-2">No actions recorded yet</p>
                            <small>Start recording to see your interactions here</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Save Recording Modal -->
<div class="modal fade" id="saveRecordingModal" tabindex="-1" aria-labelledby="saveRecordingModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="saveRecordingModalLabel">Save Test Case</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="saveRecordingForm">
                    <div class="mb-3">
                        <label for="testCaseName" class="form-label">Test Case Name</label>
                        <input type="text" class="form-control" id="testCaseName" required placeholder="Enter test case name">
                        <div class="form-text">Choose a unique name for your test case</div>
                    </div>
                    <div class="mb-3">
                        <label for="testCaseDescription" class="form-label">Description (Optional)</label>
                        <textarea class="form-control" id="testCaseDescription" rows="3" placeholder="Describe what this test case does"></textarea>
                    </div>
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        <strong>Summary:</strong> <span id="recordingSummary">0 actions recorded</span>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmSaveRecording">Save Test Case</button>
            </div>
        </div>
    </div>
</div>

<style>
/* Recorder-specific styles */
.recording-status .badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
}

.recording-info {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.375rem;
    border: 1px solid #dee2e6;
}

.recorded-actions-list {
    max-height: 600px;
    overflow-y: auto;
}

.recorded-action-item {
    border-bottom: 1px solid #dee2e6;
    padding: 1rem;
    transition: background-color 0.2s;
}

.recorded-action-item:hover {
    background-color: #f8f9fa;
}

.recorded-action-item:last-child {
    border-bottom: none;
}

.action-type-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.action-timestamp {
    font-size: 0.8rem;
    color: #6c757d;
}

.action-details {
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

.recording-pulse {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 20px;
    height: 20px;
    background-color: #dc3545;
    border-radius: 50%;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
    }
    
    70% {
        transform: scale(1);
        box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
    }
    
    100% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
    }
}

.action-play-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

/* Device screen interaction styles */
#recorderDeviceScreen {
    cursor: crosshair;
}

#recorderDeviceScreen.recording {
    border: 2px solid #dc3545;
    box-shadow: 0 0 10px rgba(220, 53, 69, 0.3);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .recorded-actions-list {
        max-height: 400px;
    }
    
    .recording-info .row {
        text-align: center;
    }
}
</style>