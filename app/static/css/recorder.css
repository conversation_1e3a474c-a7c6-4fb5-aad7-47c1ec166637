/* 
Mobile App Test Recorder Styles
Provides styling for the recorder interface including controls, device screen, and action list
*/

/* Recorder Tab Styles */
.recorder-container {
    padding: 20px;
    background: #f8f9fa;
    min-height: 100vh;
}

.recorder-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.recorder-header h2 {
    margin: 0;
    font-weight: 600;
}

.recorder-header p {
    margin: 5px 0 0 0;
    opacity: 0.9;
}

/* Recording Controls */
.recording-controls {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.control-buttons {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.record-btn {
    background: #dc3545;
    border: none;
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.record-btn:hover {
    background: #c82333;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.record-btn.recording {
    background: #28a745;
    animation: pulse-recording 2s infinite;
}

@keyframes pulse-recording {
    0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
}

.stop-btn {
    background: #6c757d;
    border: none;
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.stop-btn:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.play-btn {
    background: #007bff;
    border: none;
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.play-btn:hover {
    background: #0056b3;
    transform: translateY(-2px);
}

.save-btn {
    background: #28a745;
    border: none;
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.save-btn:hover {
    background: #1e7e34;
    transform: translateY(-2px);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Recording Status */
.recording-status {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-left: auto;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #6c757d;
    transition: all 0.3s ease;
}

.status-indicator.recording {
    background: #dc3545;
    animation: blink 1s infinite;
}

.status-indicator.stopped {
    background: #6c757d;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

.status-text {
    font-weight: 600;
    color: #495057;
}

/* Main Content Layout */
.recorder-content {
    display: flex;
    gap: 20px;
    height: calc(100vh - 200px);
}

/* Device Screen Container */
.device-screen-container {
    flex: 1;
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
}

.device-screen-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.device-info {
    font-size: 14px;
    color: #6c757d;
}

.screen-wrapper {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #f8f9fa;
    border-radius: 10px;
    position: relative;
    overflow: hidden;
}

.device-screen {
    max-width: 100%;
    max-height: 100%;
    border-radius: 20px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
    cursor: crosshair;
    transition: all 0.3s ease;
}

.device-screen:hover {
    transform: scale(1.02);
}

.device-screen.recording {
    border: 3px solid #dc3545;
    animation: glow-recording 2s infinite;
}

@keyframes glow-recording {
    0%, 100% { box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3), 0 0 20px rgba(220, 53, 69, 0.5); }
    50% { box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3), 0 0 30px rgba(220, 53, 69, 0.8); }
}

/* Action List Container */
.action-list-container {
    width: 400px;
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
}

.action-list-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.action-count {
    background: #007bff;
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
}

.action-list {
    flex: 1;
    overflow-y: auto;
    max-height: 500px;
}

.action-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
    position: relative;
}

.action-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.action-item.executing {
    background: #fff3cd;
    border-color: #ffeaa7;
    animation: highlight 1s ease-in-out;
}

@keyframes highlight {
    0%, 100% { background: #fff3cd; }
    50% { background: #ffd93d; }
}

.action-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 8px;
}

.action-type {
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.action-id {
    font-size: 11px;
    color: #6c757d;
    font-family: monospace;
}

.action-details {
    font-size: 12px;
    color: #6c757d;
    line-height: 1.4;
}

.action-controls {
    position: absolute;
    top: 8px;
    right: 8px;
    display: flex;
    gap: 5px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.action-item:hover .action-controls {
    opacity: 1;
}

.action-play-btn {
    background: #007bff;
    border: none;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-play-btn:hover {
    background: #0056b3;
}

/* Save Modal Styles */
.save-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.save-modal-content {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
}

.save-modal h3 {
    margin-top: 0;
    color: #495057;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 1px solid #ced4da;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.modal-buttons {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

.modal-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.modal-btn.primary {
    background: #007bff;
    color: white;
}

.modal-btn.primary:hover {
    background: #0056b3;
}

.modal-btn.secondary {
    background: #6c757d;
    color: white;
}

.modal-btn.secondary:hover {
    background: #5a6268;
}

/* Touch Indicator Animation */
@keyframes pulse {
    0% { transform: scale(1); opacity: 0.7; }
    50% { transform: scale(1.2); opacity: 1; }
    100% { transform: scale(1); opacity: 0.7; }
}

#touch-indicator {
    animation: pulse 0.5s infinite;
}

/* Gesture Trail Effect */
.gesture-trail {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(0, 123, 255, 0.6);
    border-radius: 50%;
    pointer-events: none;
    z-index: 999;
    animation: fade-out 1s ease-out forwards;
}

@keyframes fade-out {
    0% { opacity: 1; transform: scale(1); }
    100% { opacity: 0; transform: scale(0.5); }
}

/* Recording Visual Feedback */
.device-screen.recording::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 3px solid #dc3545;
    border-radius: 20px;
    pointer-events: none;
    animation: recording-border 2s infinite;
}

@keyframes recording-border {
    0%, 100% { border-color: #dc3545; }
    50% { border-color: #ff6b6b; }
}

/* Action Capture Feedback */
.action-captured {
    position: absolute;
    background: rgba(40, 167, 69, 0.9);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    pointer-events: none;
    z-index: 1001;
    animation: action-feedback 2s ease-out forwards;
}

@keyframes action-feedback {
    0% { opacity: 1; transform: translateY(0); }
    100% { opacity: 0; transform: translateY(-20px); }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .recorder-content {
        flex-direction: column;
        height: auto;
    }

    .action-list-container {
        width: 100%;
        max-height: 400px;
    }
}

@media (max-width: 768px) {
    .control-buttons {
        justify-content: center;
    }

    .recording-status {
        margin-left: 0;
        margin-top: 15px;
    }

    .recorder-content {
        gap: 15px;
    }

    .device-screen-container,
    .action-list-container {
        padding: 15px;
    }
}
