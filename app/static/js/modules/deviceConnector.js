/**
 * Device Connector Module
 * Handles device selection from URL parameters and connection with Appium
 */

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Device Connector module initialized');
    
    // Check for the device ID and platform in URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const deviceId = urlParams.get('deviceId');
    const platform = urlParams.get('platform');
    
    // Log the deviceId and platform for debugging
    console.log('URL Parameters - deviceId:', deviceId, 'platform:', platform);
    
    if (deviceId) {
        // Try to find the device selector
        const deviceSelect = document.querySelector('#device-selector');
        
        if (deviceSelect) {
            console.log('Found device selector, setting value to:', deviceId);
            
            // Create a custom option for the device ID
            const option = document.createElement('option');
            option.value = deviceId;
            option.textContent = deviceId || 'Unknown Device';
            option.selected = true;
            
            // Store platform as a data attribute if available
            if (platform) {
                option.dataset.platform = platform;
                console.log(`Setting data-platform attribute to: ${platform}`);
            }
            
            // Clear existing options and add the new one
            deviceSelect.innerHTML = '';
            deviceSelect.appendChild(option);
            
            // Make the selector read-only
            deviceSelect.setAttribute('disabled', 'disabled');
            
            // Remove any existing device-connector-info elements to prevent duplicates
            const existingInfo = document.querySelector('.device-connector-info');
            if (existingInfo) {
                existingInfo.remove();
            }
            
            // Add a clean, simple message showing the device details
            const deviceInfoBox = document.createElement('div');
            deviceInfoBox.className = 'device-connector-info';
            
            // Ensure our styles take precedence
            const resetCSS = document.createElement('style');
            resetCSS.textContent = `
                .device-connector-info {
                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif !important;
                    line-height: 1.5 !important;
                    box-sizing: border-box !important;
                    max-width: 100% !important;
                }
                .device-connector-info * {
                    box-sizing: border-box !important;
                }
            `;
            document.head.appendChild(resetCSS);
            
            deviceInfoBox.style.marginTop = '15px';
            deviceInfoBox.style.padding = '10px 15px';
            deviceInfoBox.style.backgroundColor = '#d1ecf1';
            deviceInfoBox.style.borderRadius = '5px';
            deviceInfoBox.style.fontSize = '14px';
            deviceInfoBox.style.border = '1px solid #bee5eb';
            deviceInfoBox.style.color = '#0c5460';
            deviceInfoBox.style.display = 'block';
            deviceInfoBox.style.width = '100%';
            
            // Create more detailed info with all inline styles
            deviceInfoBox.innerHTML = `
                <div style="margin-bottom: 8px; display: block; width: 100%;">
                    <strong style="font-weight: bold;">Device ID:</strong> <span style="word-break: break-all;">${deviceId}</span>
                </div>
                <div style="margin-bottom: 8px; display: block; width: 100%;">
                    <strong style="font-weight: bold;">Platform:</strong> ${platform || 'Unknown'}
                </div>
                <div style="color: #155724; font-weight: bold; background-color: #d4edda; padding: 5px 10px; border-radius: 3px; margin-top: 5px; border: 1px solid #c3e6cb; display: block; text-align: center;">
                    Click the "Connect" button above to establish connection with the device.
                </div>
            `;
            
            // Find the right container to insert the info
            // Target the card body that contains the device selector
            let containerElement = document.querySelector('.card-body .input-group');
            
            // Fallback to direct parent if specific containers not found
            if (!containerElement) {
                containerElement = deviceSelect.closest('.input-group') || deviceSelect.parentNode;
            }
            
            // Insert the info box after the device selector input group
            if (containerElement && containerElement.parentNode) {
                containerElement.parentNode.insertBefore(
                    deviceInfoBox, 
                    containerElement.nextSibling
                );
            }
            
            console.log('Device info displayed for:', deviceId, 'Platform:', platform);
            
            // Highlight the connect button to draw attention to it
            const connectButton = document.querySelector('#connect-button');
            if (connectButton) {
                // Add pulsing effect to the connect button
                connectButton.style.animation = 'pulse 2s infinite';
                connectButton.style.fontWeight = 'bold';
                
                // Add keyframes for the pulse animation if not already added
                if (!document.querySelector('#pulse-animation')) {
                    const style = document.createElement('style');
                    style.id = 'pulse-animation';
                    style.textContent = `
                        @keyframes pulse {
                            0% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7); }
                            70% { box-shadow: 0 0 0 10px rgba(52, 152, 219, 0); }
                            100% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0); }
                        }
                    `;
                    document.head.appendChild(style);
                }
                
                // Add a listener for the connect button
                connectButton.addEventListener('click', function() {
                    // Remove the animation once clicked
                    connectButton.style.animation = 'none';
                    console.log('Connect button clicked for device:', deviceId, 'Platform:', platform);
                });
            } else {
                console.error('Connect button not found with ID #connect-button');
            }
        } else {
            console.error('Device selector not found with ID #device-selector');
        }
    } else {
        console.log('No deviceId found in URL parameters');
    }
}); 