/**
 * Action Capture Module
 * 
 * Handles the capture and processing of user interactions during recording sessions.
 * Converts raw interaction data into structured action format compatible with the test execution engine.
 */

class ActionCapture {
    constructor() {
        this.platform = 'ios';
        this.supportedActions = [
            'tap', 'double_tap', 'long_press', 'swipe', 'text_input',
            'scroll', 'pinch', 'rotate', 'home_button'
        ];
    }
    
    /**
     * Process raw action data into structured format
     * @param {Object} actionData - Raw action data from user interaction
     * @returns {Object} Processed action in test case format
     */
    processAction(actionData) {
        try {
            const actionType = actionData.type || 'unknown';
            
            if (!this.supportedActions.includes(actionType)) {
                console.warn(`Unsupported action type: ${actionType}`);
                return this.createUnknownAction(actionData);
            }
            
            // Process the action using the appropriate handler
            let processedAction;
            
            switch (actionType) {
                case 'tap':
                    processedAction = this.processTapAction(actionData);
                    break;
                case 'double_tap':
                    processedAction = this.processDoubleTapAction(actionData);
                    break;
                case 'long_press':
                    processedAction = this.processLongPressAction(actionData);
                    break;
                case 'swipe':
                    processedAction = this.processSwipeAction(actionData);
                    break;
                case 'text_input':
                    processedAction = this.processTextInputAction(actionData);
                    break;
                case 'scroll':
                    processedAction = this.processScrollAction(actionData);
                    break;
                case 'pinch':
                    processedAction = this.processPinchAction(actionData);
                    break;
                case 'rotate':
                    processedAction = this.processRotateAction(actionData);
                    break;
                case 'home_button':
                    processedAction = this.processHomeButtonAction(actionData);
                    break;
                default:
                    processedAction = this.createUnknownAction(actionData);
            }
            
            // Add common fields
            processedAction.action_id = this.generateActionId();
            processedAction.timestamp = Date.now();
            processedAction.platform = this.platform;
            
            console.debug(`Processed ${actionType} action:`, processedAction.action_id);
            
            return processedAction;
            
        } catch (error) {
            console.error('Error processing action:', error);
            return this.createErrorAction(actionData, error.message);
        }
    }
    
    /**
     * Process tap/click action
     */
    processTapAction(actionData) {
        const x = actionData.x || 0;
        const y = actionData.y || 0;
        
        const action = {
            type: 'tap',
            method: 'coordinates',
            x: x,
            y: y,
            timeout: 10,
            interval: 0.5
        };
        
        // Try to identify element at coordinates
        const elementInfo = this.identifyElement(x, y, actionData.page_source);
        if (elementInfo && elementInfo.locator_type && elementInfo.locator_value) {
            action.method = 'locator';
            action.locator_type = elementInfo.locator_type;
            action.locator_value = elementInfo.locator_value;
        }
        
        return action;
    }
    
    /**
     * Process double tap action
     */
    processDoubleTapAction(actionData) {
        const x = actionData.x || 0;
        const y = actionData.y || 0;
        
        const action = {
            type: 'doubleTap',
            method: 'coordinates',
            x: x,
            y: y,
            timeout: 10
        };
        
        const elementInfo = this.identifyElement(x, y, actionData.page_source);
        if (elementInfo && elementInfo.locator_type && elementInfo.locator_value) {
            action.method = 'locator';
            action.locator_type = elementInfo.locator_type;
            action.locator_value = elementInfo.locator_value;
        }
        
        return action;
    }
    
    /**
     * Process long press action
     */
    processLongPressAction(actionData) {
        const x = actionData.x || 0;
        const y = actionData.y || 0;
        const duration = actionData.duration || 1000;
        
        const action = {
            type: 'longPress',
            method: 'coordinates',
            x: x,
            y: y,
            duration: duration,
            timeout: 10
        };
        
        const elementInfo = this.identifyElement(x, y, actionData.page_source);
        if (elementInfo && elementInfo.locator_type && elementInfo.locator_value) {
            action.method = 'locator';
            action.locator_type = elementInfo.locator_type;
            action.locator_value = elementInfo.locator_value;
        }
        
        return action;
    }
    
    /**
     * Process swipe gesture
     */
    processSwipeAction(actionData) {
        const startX = actionData.start_x || actionData.start?.x || 0;
        const startY = actionData.start_y || actionData.start?.y || 0;
        const endX = actionData.end_x || actionData.end?.x || 0;
        const endY = actionData.end_y || actionData.end?.y || 0;
        const duration = actionData.duration || 1000;
        
        // Determine swipe direction
        const direction = this.calculateSwipeDirection(startX, startY, endX, endY);
        
        return {
            type: 'swipe',
            direction: direction,
            start_x: startX,
            start_y: startY,
            end_x: endX,
            end_y: endY,
            duration: duration,
            timeout: 10
        };
    }
    
    /**
     * Process text input action
     */
    processTextInputAction(actionData) {
        const text = actionData.text || '';
        const x = actionData.x || 0;
        const y = actionData.y || 0;
        const clearFirst = actionData.clear_first || false;
        
        const action = {
            type: 'sendKeys',
            text: text,
            clear_first: clearFirst,
            timeout: 10
        };
        
        const elementInfo = this.identifyElement(x, y, actionData.page_source);
        if (elementInfo) {
            Object.assign(action, elementInfo);
        }
        
        return action;
    }
    
    /**
     * Process scroll action
     */
    processScrollAction(actionData) {
        const direction = actionData.direction || 'down';
        const distance = actionData.distance || 500;
        
        return {
            type: 'scroll',
            direction: direction,
            distance: distance,
            timeout: 10
        };
    }
    
    /**
     * Process pinch gesture
     */
    processPinchAction(actionData) {
        const scale = actionData.scale || 1.0;
        const centerX = actionData.center_x || 0;
        const centerY = actionData.center_y || 0;
        
        return {
            type: 'pinch',
            scale: scale,
            center_x: centerX,
            center_y: centerY,
            timeout: 10
        };
    }
    
    /**
     * Process rotation gesture
     */
    processRotateAction(actionData) {
        const angle = actionData.angle || 0;
        const centerX = actionData.center_x || 0;
        const centerY = actionData.center_y || 0;
        
        return {
            type: 'rotate',
            angle: angle,
            center_x: centerX,
            center_y: centerY,
            timeout: 10
        };
    }
    
    /**
     * Process home button action
     */
    processHomeButtonAction(actionData) {
        return {
            type: 'iosFunctions',
            function_name: 'home',
            timeout: 10
        };
    }
    
    /**
     * Attempt to identify element at given coordinates
     */
    identifyElement(x, y, pageSource) {
        // This would integrate with the existing element identification logic
        // For now, return basic coordinate info
        // TODO: Implement actual element identification using page source analysis
        
        if (pageSource) {
            // Parse page source and find element at coordinates
            // This would use existing element identification logic
        }
        
        return {};
    }
    
    /**
     * Calculate swipe direction from coordinates
     */
    calculateSwipeDirection(startX, startY, endX, endY) {
        const dx = endX - startX;
        const dy = endY - startY;
        
        if (Math.abs(dx) > Math.abs(dy)) {
            return dx > 0 ? 'right' : 'left';
        } else {
            return dy > 0 ? 'down' : 'up';
        }
    }
    
    /**
     * Generate unique action ID
     */
    generateActionId() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < 10; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
    
    /**
     * Create action for unknown action type
     */
    createUnknownAction(actionData) {
        return {
            type: 'unknown',
            raw_data: actionData,
            error: 'Unsupported action type'
        };
    }
    
    /**
     * Create action for processing errors
     */
    createErrorAction(actionData, error) {
        return {
            type: 'error',
            raw_data: actionData,
            error: error,
            action_id: this.generateActionId(),
            timestamp: Date.now()
        };
    }
}
