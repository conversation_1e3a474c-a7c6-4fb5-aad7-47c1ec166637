/**
 * Recorder Manager Module
 * 
 * Manages the test recording functionality including session management,
 * device interaction capture, and integration with existing systems.
 */

class RecorderManager {
    constructor() {
        this.isRecording = false;
        this.sessionId = null;
        this.deviceId = null;
        this.capturedActions = [];
        this.actionCapture = new ActionCapture();
        this.deviceScreen = null;
        this.actionList = null;
        
        this.init();
    }
    
    init() {
        console.log('Initializing Recorder Manager');
        this.setupEventListeners();
        this.setupDeviceScreen();
        this.setupActionList();
    }
    
    setupEventListeners() {
        // Recording control buttons
        const recordBtn = document.getElementById('record-btn');
        const stopBtn = document.getElementById('stop-btn');
        const playBtn = document.getElementById('play-btn');
        const saveBtn = document.getElementById('save-btn');
        
        if (recordBtn) {
            recordBtn.addEventListener('click', () => this.startRecording());
        }
        
        if (stopBtn) {
            stopBtn.addEventListener('click', () => this.stopRecording());
        }
        
        if (playBtn) {
            playBtn.addEventListener('click', () => this.playRecording());
        }
        
        if (saveBtn) {
            saveBtn.addEventListener('click', () => this.showSaveModal());
        }
        
        // Save modal events
        const saveModalBtn = document.getElementById('save-modal-btn');
        const cancelModalBtn = document.getElementById('cancel-modal-btn');
        
        if (saveModalBtn) {
            saveModalBtn.addEventListener('click', () => this.saveTestCase());
        }
        
        if (cancelModalBtn) {
            cancelModalBtn.addEventListener('click', () => this.hideSaveModal());
        }
    }
    
    setupDeviceScreen() {
        this.deviceScreen = document.getElementById('device-screen');
        
        if (this.deviceScreen) {
            // Add click event listener for recording interactions
            this.deviceScreen.addEventListener('click', (event) => {
                if (this.isRecording) {
                    this.captureScreenInteraction(event);
                }
            });
            
            // Add other interaction listeners
            this.deviceScreen.addEventListener('dblclick', (event) => {
                if (this.isRecording) {
                    this.captureScreenInteraction(event, 'double_tap');
                }
            });
            
            // Add touch events for mobile devices
            this.setupTouchEvents();
        }
    }
    
    setupTouchEvents() {
        if (!this.deviceScreen) return;

        let touchStartTime = 0;
        let touchStartPos = { x: 0, y: 0 };
        let touchMovePositions = [];
        let isMultiTouch = false;
        let lastTapTime = 0;

        this.deviceScreen.addEventListener('touchstart', (event) => {
            if (this.isRecording) {
                event.preventDefault();
                touchStartTime = Date.now();
                isMultiTouch = event.touches.length > 1;
                touchMovePositions = [];

                const touch = event.touches[0];
                const rect = this.deviceScreen.getBoundingClientRect();
                touchStartPos = {
                    x: touch.clientX - rect.left,
                    y: touch.clientY - rect.top
                };

                // Add visual feedback
                this.addTouchIndicator(touchStartPos.x, touchStartPos.y);
            }
        });

        this.deviceScreen.addEventListener('touchmove', (event) => {
            if (this.isRecording) {
                event.preventDefault();
                const touch = event.touches[0];
                const rect = this.deviceScreen.getBoundingClientRect();
                const currentPos = {
                    x: touch.clientX - rect.left,
                    y: touch.clientY - rect.top,
                    timestamp: Date.now()
                };

                touchMovePositions.push(currentPos);

                // Update visual feedback for swipe
                this.updateTouchIndicator(currentPos.x, currentPos.y);
            }
        });

        this.deviceScreen.addEventListener('touchend', (event) => {
            if (this.isRecording) {
                event.preventDefault();
                const touchDuration = Date.now() - touchStartTime;
                const touch = event.changedTouches[0];
                const rect = this.deviceScreen.getBoundingClientRect();
                const touchEndPos = {
                    x: touch.clientX - rect.left,
                    y: touch.clientY - rect.top
                };

                // Remove visual feedback
                this.removeTouchIndicator();

                // Determine interaction type based on duration, movement, and multi-touch
                if (isMultiTouch) {
                    this.captureScreenInteraction(event, 'pinch', {
                        start: touchStartPos,
                        end: touchEndPos,
                        duration: touchDuration
                    });
                } else if (touchDuration > 800) {
                    this.captureScreenInteraction(event, 'long_press', {
                        duration: touchDuration
                    });
                } else {
                    const distance = Math.sqrt(
                        Math.pow(touchEndPos.x - touchStartPos.x, 2) +
                        Math.pow(touchEndPos.y - touchStartPos.y, 2)
                    );

                    if (distance > 30) {
                        // Analyze movement pattern for gesture recognition
                        const gestureType = this.analyzeGesture(touchMovePositions);
                        this.captureScreenInteraction(event, gestureType, {
                            start: touchStartPos,
                            end: touchEndPos,
                            duration: touchDuration,
                            path: touchMovePositions
                        });
                    } else {
                        // Check for double tap
                        const currentTime = Date.now();
                        if (currentTime - lastTapTime < 300) {
                            this.captureScreenInteraction(event, 'double_tap');
                        } else {
                            this.captureScreenInteraction(event, 'tap');
                        }
                        lastTapTime = currentTime;
                    }
                }
            }
        });

        // Add keyboard event listeners for text input detection
        document.addEventListener('keydown', (event) => {
            if (this.isRecording && this.isTextInputEvent(event)) {
                this.captureTextInput(event);
            }
        });
    }
    
    setupActionList() {
        this.actionList = document.getElementById('action-list');
        this.updateActionCount();
    }
    
    async startRecording() {
        try {
            // Get current device ID from session or device selector
            this.deviceId = this.getCurrentDeviceId();
            
            if (!this.deviceId) {
                this.showError('Please connect to a device first');
                return;
            }
            
            // Generate session ID
            this.sessionId = this.generateSessionId();
            
            // Start recording session on backend
            const response = await fetch('/api/recorder/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    device_id: this.deviceId,
                    session_id: this.sessionId
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.isRecording = true;
                this.capturedActions = [];
                this.updateRecordingUI();
                this.updateActionList();
                console.log('Recording started successfully');
            } else {
                this.showError(result.error || 'Failed to start recording');
            }
            
        } catch (error) {
            console.error('Error starting recording:', error);
            this.showError('Failed to start recording');
        }
    }
    
    async stopRecording() {
        try {
            if (!this.isRecording || !this.sessionId) {
                return;
            }
            
            // Stop recording session on backend
            const response = await fetch('/api/recorder/stop', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    session_id: this.sessionId
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.isRecording = false;
                this.capturedActions = result.actions || [];
                this.updateRecordingUI();
                this.updateActionList();
                console.log('Recording stopped successfully');
            } else {
                this.showError(result.error || 'Failed to stop recording');
            }
            
        } catch (error) {
            console.error('Error stopping recording:', error);
            this.showError('Failed to stop recording');
        }
    }
    
    async captureScreenInteraction(event, actionType = 'tap', extraData = {}) {
        if (!this.isRecording) return;
        
        const rect = this.deviceScreen.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        
        // Scale coordinates to actual device screen size
        const scaledCoords = this.scaleCoordinates(x, y);
        
        const actionData = {
            type: actionType,
            x: scaledCoords.x,
            y: scaledCoords.y,
            timestamp: Date.now(),
            ...extraData
        };
        
        try {
            // Send action to backend for processing
            const response = await fetch('/api/recorder/capture', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    session_id: this.sessionId,
                    action_data: actionData
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                // Add to local action list for immediate UI update
                this.capturedActions.push({
                    action_id: result.action_id,
                    type: result.action_type,
                    timestamp: result.timestamp,
                    ...actionData
                });
                
                this.updateActionList();
                console.log('Action captured:', result.action_id);
            } else {
                console.error('Failed to capture action:', result.error);
            }
            
        } catch (error) {
            console.error('Error capturing action:', error);
        }
    }
    
    scaleCoordinates(x, y) {
        // Get actual device screen dimensions from the image
        const naturalWidth = this.deviceScreen.naturalWidth || this.deviceScreen.width;
        const naturalHeight = this.deviceScreen.naturalHeight || this.deviceScreen.height;
        const displayWidth = this.deviceScreen.width;
        const displayHeight = this.deviceScreen.height;
        
        const scaleX = naturalWidth / displayWidth;
        const scaleY = naturalHeight / displayHeight;
        
        return {
            x: Math.round(x * scaleX),
            y: Math.round(y * scaleY)
        };
    }
    
    updateRecordingUI() {
        const recordBtn = document.getElementById('record-btn');
        const stopBtn = document.getElementById('stop-btn');
        const statusIndicator = document.querySelector('.status-indicator');
        const statusText = document.querySelector('.status-text');
        
        if (this.isRecording) {
            if (recordBtn) {
                recordBtn.classList.add('recording');
                recordBtn.disabled = true;
            }
            if (stopBtn) {
                stopBtn.disabled = false;
            }
            if (statusIndicator) {
                statusIndicator.classList.add('recording');
            }
            if (statusText) {
                statusText.textContent = 'Recording...';
            }
            if (this.deviceScreen) {
                this.deviceScreen.classList.add('recording');
            }
        } else {
            if (recordBtn) {
                recordBtn.classList.remove('recording');
                recordBtn.disabled = false;
            }
            if (stopBtn) {
                stopBtn.disabled = true;
            }
            if (statusIndicator) {
                statusIndicator.classList.remove('recording');
                statusIndicator.classList.add('stopped');
            }
            if (statusText) {
                statusText.textContent = 'Stopped';
            }
            if (this.deviceScreen) {
                this.deviceScreen.classList.remove('recording');
            }
        }
    }
    
    updateActionList() {
        if (!this.actionList) return;
        
        this.actionList.innerHTML = '';
        
        this.capturedActions.forEach((action, index) => {
            const actionElement = this.createActionElement(action, index);
            this.actionList.appendChild(actionElement);
        });
        
        this.updateActionCount();
    }
    
    createActionElement(action, index) {
        const actionDiv = document.createElement('div');
        actionDiv.className = 'action-item';
        actionDiv.innerHTML = `
            <div class="action-header">
                <span class="action-type">${action.type}</span>
                <span class="action-id">${action.action_id || 'N/A'}</span>
            </div>
            <div class="action-details">
                ${this.formatActionDetails(action)}
            </div>
            <div class="action-controls">
                <button class="action-play-btn" onclick="recorderManager.playAction('${action.action_id}')">
                    Play
                </button>
            </div>
        `;
        
        return actionDiv;
    }
    
    formatActionDetails(action) {
        let details = [];
        
        if (action.x !== undefined && action.y !== undefined) {
            details.push(`Coordinates: (${action.x}, ${action.y})`);
        }
        
        if (action.text) {
            details.push(`Text: "${action.text}"`);
        }
        
        if (action.locator_type && action.locator_value) {
            details.push(`Locator: ${action.locator_type} = "${action.locator_value}"`);
        }
        
        return details.join('<br>');
    }
    
    updateActionCount() {
        const actionCount = document.querySelector('.action-count');
        if (actionCount) {
            actionCount.textContent = this.capturedActions.length;
        }
    }
    
    getCurrentDeviceId() {
        // Get device ID from session storage or device selector
        return localStorage.getItem('currentDeviceId') || 
               sessionStorage.getItem('device_id') ||
               document.getElementById('device-select')?.value;
    }
    
    generateSessionId() {
        return 'rec_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    showSaveModal() {
        const modal = document.getElementById('save-modal');
        if (modal) {
            modal.style.display = 'flex';
        }
    }
    
    hideSaveModal() {
        const modal = document.getElementById('save-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }
    
    async saveTestCase() {
        const testCaseName = document.getElementById('test-case-name')?.value;
        
        if (!testCaseName) {
            this.showError('Please enter a test case name');
            return;
        }
        
        try {
            const response = await fetch('/api/recorder/save', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    session_id: this.sessionId,
                    test_case_name: testCaseName
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.hideSaveModal();
                this.showSuccess(`Test case "${testCaseName}" saved successfully`);
                this.resetRecorder();
            } else {
                this.showError(result.error || 'Failed to save test case');
            }
            
        } catch (error) {
            console.error('Error saving test case:', error);
            this.showError('Failed to save test case');
        }
    }
    
    async playRecording() {
        if (this.capturedActions.length === 0) {
            this.showError('No actions to play');
            return;
        }

        try {
            // Create test case data for playback
            const testCaseData = {
                name: 'Recorded_Session_' + Date.now(),
                created: new Date().toISOString(),
                actions: this.capturedActions
            };

            const playbackId = 'playback_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

            // Start playback session
            const startResponse = await fetch('/api/recorder/playback/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    playback_id: playbackId,
                    test_case_data: testCaseData,
                    device_id: this.deviceId,
                    options: {
                        continue_on_failure: true,
                        action_delay: 1.0
                    }
                })
            });

            const startResult = await startResponse.json();

            if (startResult.success) {
                this.showInfo('Starting playback...');

                // Execute playback
                const executeResponse = await fetch(`/api/recorder/playback/execute/${playbackId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const executeResult = await executeResponse.json();

                if (executeResult.success) {
                    const successRate = (executeResult.success_rate * 100).toFixed(1);
                    this.showSuccess(`Playback completed! Success rate: ${successRate}% (${executeResult.successful_actions}/${executeResult.total_actions} actions)`);

                    // Highlight failed actions if any
                    if (executeResult.failed_action_indices.length > 0) {
                        this.highlightFailedActions(executeResult.failed_action_indices);
                    }
                } else {
                    this.showError(`Playback failed: ${executeResult.error}`);
                }
            } else {
                this.showError(`Failed to start playback: ${startResult.error}`);
            }

        } catch (error) {
            console.error('Error during playback:', error);
            this.showError('Failed to execute playback');
        }
    }

    async playAction(actionId) {
        const actionIndex = this.capturedActions.findIndex(a => a.action_id === actionId);
        if (actionIndex === -1) {
            this.showError('Action not found');
            return;
        }

        try {
            // Create temporary test case for single action
            const testCaseData = {
                name: 'Single_Action_' + actionId,
                created: new Date().toISOString(),
                actions: this.capturedActions
            };

            const playbackId = 'single_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

            // Start playback session
            const startResponse = await fetch('/api/recorder/playback/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    playback_id: playbackId,
                    test_case_data: testCaseData,
                    device_id: this.deviceId
                })
            });

            const startResult = await startResponse.json();

            if (startResult.success) {
                // Execute single action
                const executeResponse = await fetch(`/api/recorder/playback/execute-action/${playbackId}/${actionIndex}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const executeResult = await executeResponse.json();

                if (executeResult.success) {
                    this.showSuccess(`Action executed successfully in ${executeResult.execution_time.toFixed(2)}s`);
                    this.highlightActionExecution(actionIndex, true);
                } else {
                    this.showError(`Action execution failed: ${executeResult.error}`);
                    this.highlightActionExecution(actionIndex, false);
                }

                // Clean up playback session
                await fetch(`/api/recorder/playback/stop/${playbackId}`, { method: 'POST' });
            } else {
                this.showError(`Failed to start single action playback: ${startResult.error}`);
            }

        } catch (error) {
            console.error('Error during single action playback:', error);
            this.showError('Failed to execute single action');
        }
    }

    highlightFailedActions(failedIndices) {
        const actionItems = document.querySelectorAll('.action-item');

        failedIndices.forEach(index => {
            if (actionItems[index]) {
                actionItems[index].classList.add('action-failed');
                actionItems[index].style.borderColor = '#dc3545';
                actionItems[index].style.backgroundColor = '#f8d7da';
            }
        });

        // Remove highlights after 5 seconds
        setTimeout(() => {
            actionItems.forEach(item => {
                item.classList.remove('action-failed');
                item.style.borderColor = '';
                item.style.backgroundColor = '';
            });
        }, 5000);
    }

    highlightActionExecution(actionIndex, success) {
        const actionItems = document.querySelectorAll('.action-item');

        if (actionItems[actionIndex]) {
            const item = actionItems[actionIndex];

            if (success) {
                item.classList.add('action-success');
                item.style.borderColor = '#28a745';
                item.style.backgroundColor = '#d4edda';
            } else {
                item.classList.add('action-failed');
                item.style.borderColor = '#dc3545';
                item.style.backgroundColor = '#f8d7da';
            }

            // Remove highlight after 3 seconds
            setTimeout(() => {
                item.classList.remove('action-success', 'action-failed');
                item.style.borderColor = '';
                item.style.backgroundColor = '';
            }, 3000);
        }
    }
    
    resetRecorder() {
        this.isRecording = false;
        this.sessionId = null;
        this.capturedActions = [];
        this.updateRecordingUI();
        this.updateActionList();
    }
    
    showError(message) {
        console.error(message);
        // Integrate with existing notification system
        if (window.showNotification) {
            window.showNotification(message, 'error');
        } else {
            alert('Error: ' + message);
        }
    }
    
    showSuccess(message) {
        console.log(message);
        // Integrate with existing notification system
        if (window.showNotification) {
            window.showNotification(message, 'success');
        } else {
            alert('Success: ' + message);
        }
    }
    
    showInfo(message) {
        console.info(message);
        // Integrate with existing notification system
        if (window.showNotification) {
            window.showNotification(message, 'info');
        } else {
            alert('Info: ' + message);
        }
    }

    /**
     * Analyze gesture pattern from touch movement positions
     */
    analyzeGesture(movePositions) {
        if (movePositions.length < 2) {
            return 'swipe';
        }

        const start = movePositions[0];
        const end = movePositions[movePositions.length - 1];

        // Calculate total distance and direction changes
        let totalDistance = 0;
        let directionChanges = 0;
        let lastDirection = null;

        for (let i = 1; i < movePositions.length; i++) {
            const prev = movePositions[i - 1];
            const curr = movePositions[i];

            const dx = curr.x - prev.x;
            const dy = curr.y - prev.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            totalDistance += distance;

            // Determine direction
            const direction = Math.abs(dx) > Math.abs(dy) ?
                (dx > 0 ? 'right' : 'left') :
                (dy > 0 ? 'down' : 'up');

            if (lastDirection && lastDirection !== direction) {
                directionChanges++;
            }
            lastDirection = direction;
        }

        // Classify gesture based on pattern
        if (directionChanges > 3) {
            return 'complex_gesture';
        } else if (totalDistance > 200) {
            return 'swipe';
        } else {
            return 'drag';
        }
    }

    /**
     * Add visual touch indicator
     */
    addTouchIndicator(x, y) {
        this.removeTouchIndicator(); // Remove any existing indicator

        const indicator = document.createElement('div');
        indicator.id = 'touch-indicator';
        indicator.style.cssText = `
            position: absolute;
            left: ${x - 10}px;
            top: ${y - 10}px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: rgba(255, 0, 0, 0.5);
            border: 2px solid #ff0000;
            pointer-events: none;
            z-index: 1000;
            animation: pulse 0.5s infinite;
        `;

        const screenWrapper = this.deviceScreen.parentElement;
        if (screenWrapper) {
            screenWrapper.style.position = 'relative';
            screenWrapper.appendChild(indicator);
        }
    }

    /**
     * Update touch indicator position
     */
    updateTouchIndicator(x, y) {
        const indicator = document.getElementById('touch-indicator');
        if (indicator) {
            indicator.style.left = `${x - 10}px`;
            indicator.style.top = `${y - 10}px`;
        }
    }

    /**
     * Remove touch indicator
     */
    removeTouchIndicator() {
        const indicator = document.getElementById('touch-indicator');
        if (indicator) {
            indicator.remove();
        }
    }

    /**
     * Check if keyboard event is text input
     */
    isTextInputEvent(event) {
        // Check if the event is from a text input field
        const activeElement = document.activeElement;
        return activeElement && (
            activeElement.tagName === 'INPUT' ||
            activeElement.tagName === 'TEXTAREA' ||
            activeElement.contentEditable === 'true'
        );
    }

    /**
     * Capture text input action
     */
    async captureTextInput(event) {
        const activeElement = document.activeElement;
        if (!activeElement) return;

        const text = activeElement.value || activeElement.textContent || '';
        const rect = activeElement.getBoundingClientRect();
        const screenRect = this.deviceScreen.getBoundingClientRect();

        // Calculate relative position on device screen
        const x = rect.left + rect.width / 2 - screenRect.left;
        const y = rect.top + rect.height / 2 - screenRect.top;

        const actionData = {
            type: 'text_input',
            text: text,
            x: x,
            y: y,
            timestamp: Date.now()
        };

        await this.captureScreenInteraction(event, 'text_input', actionData);
    }

    /**
     * Enhanced screenshot capture for action context
     */
    async captureActionScreenshot(actionId) {
        try {
            const response = await fetch('/screenshot?t=' + Date.now());
            if (response.ok) {
                const blob = await response.blob();
                // Store screenshot data for the action
                // This would integrate with existing screenshot storage
                console.log(`Screenshot captured for action ${actionId}`);
            }
        } catch (error) {
            console.error('Error capturing action screenshot:', error);
        }
    }
}

// Initialize recorder manager when DOM is loaded
let recorderManager;
document.addEventListener('DOMContentLoaded', () => {
    if (document.getElementById('recorder-tab')) {
        recorderManager = new RecorderManager();
    }
});
