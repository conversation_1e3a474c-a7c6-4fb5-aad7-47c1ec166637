"""
Recorder Routes Module

Flask routes for the mobile app test recorder functionality.
Handles recording session management, action capture, and test case saving.
"""

import os
import json
import logging
from datetime import datetime
from flask import Blueprint, request, jsonify, session
from recorder.recorder_manager import RecorderManager
from utils.directory_paths_db import directory_paths_db

logger = logging.getLogger(__name__)

# Create blueprint
recorder_bp = Blueprint('recorder', __name__, url_prefix='/api/recorder')

# Initialize recorder manager and playback manager
recorder_manager = RecorderManager()

# Import and initialize playback manager
from recorder.playback_manager import PlaybackManager
playback_manager = PlaybackManager()


@recorder_bp.route('/start', methods=['POST'])
def start_recording():
    """Start a new recording session"""
    try:
        data = request.get_json()
        device_id = data.get('device_id')
        session_id = data.get('session_id')
        platform = data.get('platform', 'ios')
        
        if not device_id or not session_id:
            return jsonify({
                'success': False,
                'error': 'Device ID and session ID are required'
            }), 400
        
        # Start recording session
        result = recorder_manager.start_recording_session(device_id, session_id)
        
        if result['success']:
            # Store session info in Flask session
            session['recording_session_id'] = session_id
            session['recording_device_id'] = device_id
            session['recording_platform'] = platform
            
            logger.info(f"Started recording session {session_id} for device {device_id}")
            
            return jsonify(result)
        else:
            return jsonify(result), 400
            
    except Exception as e:
        logger.error(f"Error starting recording session: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500


@recorder_bp.route('/stop', methods=['POST'])
def stop_recording():
    """Stop an active recording session"""
    try:
        data = request.get_json()
        session_id = data.get('session_id') or session.get('recording_session_id')
        platform = data.get('platform') or session.get('recording_platform', 'ios')
        
        if not session_id:
            return jsonify({
                'success': False,
                'error': 'Session ID is required'
            }), 400
        
        # Stop recording session
        result = recorder_manager.stop_recording_session(session_id)
        
        if result['success']:
            logger.info(f"Stopped recording session {session_id}")
            return jsonify(result)
        else:
            return jsonify(result), 400
            
    except Exception as e:
        logger.error(f"Error stopping recording session: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500


@recorder_bp.route('/capture', methods=['POST'])
def capture_action():
    """Capture a user action during recording"""
    try:
        data = request.get_json()
        session_id = data.get('session_id') or session.get('recording_session_id')
        action_data = data.get('action_data')
        platform = data.get('platform') or session.get('recording_platform', 'ios')
        
        if not session_id or not action_data:
            return jsonify({
                'success': False,
                'error': 'Session ID and action data are required'
            }), 400
        
        # Add platform info to action data
        action_data['platform'] = platform
        
        # Capture action
        result = recorder_manager.capture_action(session_id, action_data)
        
        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 400
            
    except Exception as e:
        logger.error(f"Error capturing action: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500


@recorder_bp.route('/save', methods=['POST'])
def save_test_case():
    """Save recorded session as a test case"""
    try:
        data = request.get_json()
        session_id = data.get('session_id') or session.get('recording_session_id')
        test_case_name = data.get('test_case_name')
        platform = data.get('platform') or session.get('recording_platform', 'ios')
        
        if not session_id or not test_case_name:
            return jsonify({
                'success': False,
                'error': 'Session ID and test case name are required'
            }), 400
        
        # Get test cases directory from settings
        test_case_dir = directory_paths_db.get_path('TEST_CASES')
        
        if not test_case_dir or not os.path.exists(test_case_dir):
            return jsonify({
                'success': False,
                'error': 'Test cases directory not configured or does not exist'
            }), 400
        
        # Save test case
        result = recorder_manager.save_test_case(session_id, test_case_name, test_case_dir)
        
        if result['success']:
            # Clear session info
            session.pop('recording_session_id', None)
            session.pop('recording_device_id', None)
            session.pop('recording_platform', None)
            
            logger.info(f"Saved test case '{test_case_name}' from session {session_id}")
            return jsonify(result)
        else:
            return jsonify(result), 400
            
    except Exception as e:
        logger.error(f"Error saving test case: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500


@recorder_bp.route('/status/<session_id>', methods=['GET'])
def get_session_status(session_id):
    """Get status of a recording session"""
    try:
        result = recorder_manager.get_session_status(session_id)
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error getting session status: {str(e)}")
        return jsonify({
            'active': False,
            'error': 'Internal server error'
        }), 500


@recorder_bp.route('/cleanup/<session_id>', methods=['POST'])
def cleanup_session(session_id):
    """Clean up a recording session"""
    try:
        result = recorder_manager.cleanup_session(session_id)
        
        if result:
            # Clear session info if it matches
            if session.get('recording_session_id') == session_id:
                session.pop('recording_session_id', None)
                session.pop('recording_device_id', None)
                session.pop('recording_platform', None)
            
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'error': 'Session not found'}), 404
            
    except Exception as e:
        logger.error(f"Error cleaning up session: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500


@recorder_bp.route('/sessions', methods=['GET'])
def list_active_sessions():
    """List all active recording sessions"""
    try:
        sessions = []
        for session_id in recorder_manager.active_sessions:
            session_info = recorder_manager.get_session_status(session_id)
            if session_info.get('active'):
                sessions.append(session_info)
        
        return jsonify({
            'success': True,
            'sessions': sessions
        })
        
    except Exception as e:
        logger.error(f"Error listing sessions: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500


@recorder_bp.route('/capabilities', methods=['GET'])
def get_recorder_capabilities():
    """Get recorder capabilities and supported features"""
    try:
        platform = request.args.get('platform', 'ios')
        
        if platform == 'android':
            # Get Android-specific capabilities if available
            if hasattr(recorder_manager, 'get_android_specific_capabilities'):
                capabilities = recorder_manager.get_android_specific_capabilities()
            else:
                capabilities = {
                    'supported_locators': ['id', 'xpath', 'uiselector', 'accessibility_id'],
                    'supported_actions': [
                        'tap', 'double_tap', 'long_press', 'swipe', 'text_input',
                        'scroll', 'pinch', 'rotate', 'back_button', 'home_button'
                    ],
                    'platform_features': ['uiautomator2_integration', 'webview_context_switching']
                }
        else:
            # iOS capabilities
            capabilities = {
                'supported_locators': ['id', 'xpath', 'accessibility_id', 'class_name'],
                'supported_actions': [
                    'tap', 'double_tap', 'long_press', 'swipe', 'text_input',
                    'scroll', 'pinch', 'rotate', 'home_button'
                ],
                'platform_features': ['wda_integration', 'ios_simulator_support']
            }
        
        capabilities['platform'] = platform
        capabilities['version'] = '1.0.0'
        
        return jsonify({
            'success': True,
            'capabilities': capabilities
        })
        
    except Exception as e:
        logger.error(f"Error getting capabilities: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500


@recorder_bp.errorhandler(404)
def not_found(error):
    """Handle 404 errors"""
    return jsonify({
        'success': False,
        'error': 'Endpoint not found'
    }), 404


@recorder_bp.errorhandler(405)
def method_not_allowed(error):
    """Handle 405 errors"""
    return jsonify({
        'success': False,
        'error': 'Method not allowed'
    }), 405


@recorder_bp.route('/playback/start', methods=['POST'])
def start_playback():
    """Start playback of a recorded test case"""
    try:
        data = request.get_json()
        test_case_data = data.get('test_case_data')
        device_id = data.get('device_id')
        playback_id = data.get('playback_id')
        options = data.get('options', {})

        if not test_case_data or not device_id or not playback_id:
            return jsonify({
                'success': False,
                'error': 'Test case data, device ID, and playback ID are required'
            }), 400

        result = playback_manager.start_playback(playback_id, test_case_data, device_id, options)

        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 400

    except Exception as e:
        logger.error(f"Error starting playback: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500


@recorder_bp.route('/playback/execute/<playback_id>', methods=['POST'])
def execute_playback(playback_id):
    """Execute a full playback session"""
    try:
        result = playback_manager.execute_playback(playback_id)

        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 400

    except Exception as e:
        logger.error(f"Error executing playback: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500


@recorder_bp.route('/playback/execute-action/<playback_id>/<int:action_index>', methods=['POST'])
def execute_single_action(playback_id, action_index):
    """Execute a single action from a test case"""
    try:
        result = playback_manager.execute_single_action(playback_id, action_index)

        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 400

    except Exception as e:
        logger.error(f"Error executing single action: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500


@recorder_bp.route('/playback/stop/<playback_id>', methods=['POST'])
def stop_playback(playback_id):
    """Stop an active playback session"""
    try:
        result = playback_manager.stop_playback(playback_id)

        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 404

    except Exception as e:
        logger.error(f"Error stopping playback: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500


@recorder_bp.route('/playback/status/<playback_id>', methods=['GET'])
def get_playback_status(playback_id):
    """Get status of a playback session"""
    try:
        result = playback_manager.get_playback_status(playback_id)
        return jsonify(result)

    except Exception as e:
        logger.error(f"Error getting playback status: {str(e)}")
        return jsonify({
            'active': False,
            'error': 'Internal server error'
        }), 500


@recorder_bp.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    logger.error(f"Internal server error: {str(error)}")
    return jsonify({
        'success': False,
        'error': 'Internal server error'
    }), 500
