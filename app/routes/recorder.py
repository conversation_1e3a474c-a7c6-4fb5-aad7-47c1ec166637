"""
Recorder routes for mobile app test recording functionality
"""
import os
import json
import time
import sys
from pathlib import Path
from flask import Blueprint, jsonify, request, render_template
from flask_socketio import emit

# Add the app directory to the path
app_dir = str(Path(__file__).resolve().parent.parent)
if app_dir not in sys.path:
    sys.path.insert(0, app_dir)

from utils.appium_device_controller import AppiumDeviceController
from utils.id_generator import generate_action_id
from utils.test_case_manager import TestCaseManager
from utils.database import get_db_connection

recorder_bp = Blueprint('recorder', __name__, url_prefix='/api/recorder')

# Global recorder state
recorder_state = {
    'is_recording': False,
    'actions': [],
    'start_time': None,
    'device_controller': None,
    'session_id': None
}

def get_device_controller():
    """Get or create device controller with configured ports"""
    if recorder_state['device_controller'] is None:
        try:
            # Add parent directory to path to import config
            parent_dir = str(Path(__file__).resolve().parent.parent.parent)
            if parent_dir not in sys.path:
                sys.path.insert(0, parent_dir)
            import config
            appium_port = getattr(config, 'APPIUM_PORT', 4723)
            wda_port = getattr(config, 'WDA_PORT', 8100)
            recorder_state['device_controller'] = AppiumDeviceController(appium_port=appium_port, wda_port=wda_port)
        except ImportError:
            recorder_state['device_controller'] = AppiumDeviceController()
    return recorder_state['device_controller']

@recorder_bp.route('/status', methods=['GET'])
def get_recorder_status():
    """Get current recorder status"""
    return jsonify({
        'is_recording': recorder_state['is_recording'],
        'actions_count': len(recorder_state['actions']),
        'session_id': recorder_state['session_id'],
        'device_connected': recorder_state['device_controller'] is not None and 
                           recorder_state['device_controller'].driver is not None
    })

@recorder_bp.route('/start', methods=['POST'])
def start_recording():
    """Start a new recording session"""
    try:
        if recorder_state['is_recording']:
            return jsonify({'error': 'Recording already in progress'}), 400
        
        # Get device controller
        device_controller = get_device_controller()
        
        if not device_controller.driver:
            return jsonify({'error': 'No device connected. Please connect a device first.'}), 400
        
        # Initialize recording session
        recorder_state['is_recording'] = True
        recorder_state['actions'] = []
        recorder_state['start_time'] = time.time()
        recorder_state['session_id'] = f"rec_{int(time.time())}"
        
        # Take initial screenshot
        try:
            screenshot_path = device_controller.take_screenshot()
            if screenshot_path:
                # Add initial state action
                initial_action = {
                    'action_id': generate_action_id(),
                    'type': 'info',
                    'message': 'Recording session started',
                    'timestamp': 0,
                    'screenshot': screenshot_path
                }
                recorder_state['actions'].append(initial_action)
        except Exception as e:
            print(f"Warning: Could not take initial screenshot: {e}")
        
        return jsonify({
            'success': True,
            'session_id': recorder_state['session_id'],
            'message': 'Recording started successfully'
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@recorder_bp.route('/stop', methods=['POST'])
def stop_recording():
    """Stop the current recording session"""
    try:
        if not recorder_state['is_recording']:
            return jsonify({'error': 'No recording in progress'}), 400
        
        recorder_state['is_recording'] = False
        actions_count = len(recorder_state['actions'])
        
        return jsonify({
            'success': True,
            'actions_count': actions_count,
            'actions': recorder_state['actions'],
            'message': f'Recording stopped. Captured {actions_count} actions.'
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@recorder_bp.route('/action', methods=['POST'])
def record_action():
    """Record a user action during recording session"""
    try:
        if not recorder_state['is_recording']:
            return jsonify({'error': 'No recording in progress'}), 400
        
        action_data = request.json
        if not action_data:
            return jsonify({'error': 'No action data provided'}), 400
        
        # Calculate timestamp relative to recording start
        current_time = time.time()
        elapsed_time = current_time - recorder_state['start_time']
        
        # Generate unique action ID
        action_id = generate_action_id()
        
        # Get device controller for screenshot
        device_controller = get_device_controller()
        
        # Take screenshot after action
        screenshot_path = None
        try:
            screenshot_path = device_controller.take_screenshot()
        except Exception as e:
            print(f"Warning: Could not take screenshot: {e}")
        
        # Create action record based on type
        action = {
            'action_id': action_id,
            'type': action_data.get('type'),
            'timestamp': round(elapsed_time, 3),
            'screenshot': screenshot_path
        }
        
        # Add type-specific parameters
        if action_data['type'] == 'tap':
            action.update({
                'x': action_data.get('x'),
                'y': action_data.get('y'),
                'locator_type': 'coordinates'
            })
            
        elif action_data['type'] == 'doubleTap':
            action.update({
                'x': action_data.get('x'),
                'y': action_data.get('y'),
                'locator_type': 'coordinates'
            })
            
        elif action_data['type'] == 'swipe':
            action.update({
                'start_x': action_data.get('start_x'),
                'start_y': action_data.get('start_y'),
                'end_x': action_data.get('end_x'),
                'end_y': action_data.get('end_y'),
                'duration': action_data.get('duration', 300)
            })
            
        elif action_data['type'] == 'text':
            action.update({
                'text': action_data.get('text'),
                'x': action_data.get('x'),
                'y': action_data.get('y'),
                'locator_type': 'coordinates'
            })
            
        elif action_data['type'] == 'wait':
            action.update({
                'duration': action_data.get('duration', 1000)
            })
        
        # Add element identification if available
        try:
            if device_controller.driver and action_data['type'] in ['tap', 'doubleTap', 'text']:
                page_source = device_controller.get_page_source()
                if page_source:
                    # Try to find element at coordinates
                    x, y = action_data.get('x', 0), action_data.get('y', 0)
                    element_info = _find_element_at_coordinates(page_source, x, y)
                    if element_info:
                        action.update(element_info)
        except Exception as e:
            print(f"Warning: Could not identify element: {e}")
        
        # Add action to recording
        recorder_state['actions'].append(action)
        
        return jsonify({
            'success': True,
            'action_id': action_id,
            'actions_count': len(recorder_state['actions'])
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@recorder_bp.route('/save', methods=['POST'])
def save_recording():
    """Save the recorded test case"""
    try:
        if recorder_state['is_recording']:
            return jsonify({'error': 'Cannot save while recording is in progress'}), 400
        
        data = request.json
        test_case_name = data.get('name')
        
        if not test_case_name:
            return jsonify({'error': 'Test case name is required'}), 400
        
        if not recorder_state['actions']:
            return jsonify({'error': 'No actions recorded'}), 400
        
        # Create test case data structure
        test_case_data = {
            'name': test_case_name,
            'description': f'Recorded test case - {len(recorder_state["actions"])} actions',
            'actions': recorder_state['actions'],
            'created_by': 'recorder',
            'created_at': time.time()
        }
        
        # Save using TestCaseManager
        test_case_manager = TestCaseManager()
        
        # Check if test case name already exists
        existing_cases = test_case_manager.get_test_cases()
        if any(case.get('name') == test_case_name for case in existing_cases):
            return jsonify({'error': f'Test case "{test_case_name}" already exists'}), 400
        
        # Save the test case
        success = test_case_manager.save_test_case(test_case_data)
        
        if success:
            # Clear the recorded actions
            recorder_state['actions'] = []
            recorder_state['session_id'] = None
            
            return jsonify({
                'success': True,
                'message': f'Test case "{test_case_name}" saved successfully'
            })
        else:
            return jsonify({'error': 'Failed to save test case'}), 500
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@recorder_bp.route('/play', methods=['POST'])
def play_recording():
    """Play back the recorded actions"""
    try:
        data = request.json
        actions = data.get('actions', recorder_state['actions'])
        
        if not actions:
            return jsonify({'error': 'No actions to play'}), 400
        
        device_controller = get_device_controller()
        if not device_controller.driver:
            return jsonify({'error': 'No device connected'}), 400
        
        # Execute actions
        from action_executor import ActionExecutor
        executor = ActionExecutor(device_controller)
        
        results = []
        for action in actions:
            if action.get('type') == 'info':
                continue  # Skip info actions
                
            try:
                result = executor.execute_action(action)
                results.append({
                    'action_id': action.get('action_id'),
                    'success': result.get('success', True),
                    'message': result.get('message', 'Action executed')
                })
            except Exception as e:
                results.append({
                    'action_id': action.get('action_id'),
                    'success': False,
                    'message': str(e)
                })
        
        return jsonify({
            'success': True,
            'results': results,
            'message': f'Executed {len(results)} actions'
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@recorder_bp.route('/play-step', methods=['POST'])
def play_single_step():
    """Play back a single recorded action"""
    try:
        data = request.json
        action = data.get('action')
        
        if not action:
            return jsonify({'error': 'No action provided'}), 400
        
        device_controller = get_device_controller()
        if not device_controller.driver:
            return jsonify({'error': 'No device connected'}), 400
        
        # Execute single action
        from action_executor import ActionExecutor
        executor = ActionExecutor(device_controller)
        
        try:
            result = executor.execute_action(action)
            return jsonify({
                'success': result.get('success', True),
                'message': result.get('message', 'Action executed successfully')
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': str(e)
            })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

def _find_element_at_coordinates(page_source, x, y):
    """Find element at given coordinates from page source"""
    try:
        import xml.etree.ElementTree as ET
        root = ET.fromstring(page_source)
        
        for elem in root.findall(".//*[@bounds]"):
            bounds_str = elem.attrib.get('bounds', '')
            if bounds_str:
                try:
                    # Parse bounds format like "[0,0][100,100]"
                    bounds_parts = bounds_str.replace("][", ",").replace("[", "").replace("]", "").split(",")
                    if len(bounds_parts) == 4:
                        x1, y1, x2, y2 = map(int, bounds_parts)
                        
                        # Check if coordinates are within bounds
                        if x1 <= x <= x2 and y1 <= y <= y2:
                            element_info = {}
                            
                            # Add resource-id if available
                            resource_id = elem.attrib.get('resource-id', '')
                            if resource_id:
                                element_info['resource_id'] = resource_id
                                element_info['locator_type'] = 'resource_id'
                                element_info['locator_value'] = resource_id
                            
                            # Add text if available
                            text = elem.attrib.get('text', '')
                            if text:
                                element_info['text'] = text
                                if not element_info.get('locator_type'):
                                    element_info['locator_type'] = 'text'
                                    element_info['locator_value'] = text
                            
                            # Add class name
                            class_name = elem.attrib.get('class', '')
                            if class_name:
                                element_info['class'] = class_name
                            
                            return element_info
                            
                except Exception as e:
                    continue
        
        return None
        
    except Exception as e:
        print(f"Error finding element at coordinates: {e}")
        return None