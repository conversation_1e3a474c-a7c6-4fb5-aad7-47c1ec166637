"""
Recorder Routes Module - Android Platform

Flask routes for the Android mobile app test recorder functionality.
Handles recording session management, action capture, and test case saving for Android devices.
"""

import os
import json
import logging
from datetime import datetime
from flask import Blueprint, request, jsonify, session
from recorder.recorder_manager import RecorderManager
from utils.directory_paths_db import directory_paths_db

logger = logging.getLogger(__name__)

# Create blueprint
recorder_bp = Blueprint('recorder', __name__, url_prefix='/api/recorder')

# Initialize Android recorder manager and playback manager
recorder_manager = RecorderManager()

# Import and initialize Android playback manager
from recorder.playback_manager import PlaybackManager
playback_manager = PlaybackManager()


@recorder_bp.route('/start', methods=['POST'])
def start_recording():
    """Start a new Android recording session"""
    try:
        data = request.get_json()
        device_id = data.get('device_id')
        session_id = data.get('session_id')
        platform = data.get('platform', 'android')
        
        if not device_id or not session_id:
            return jsonify({
                'success': False,
                'error': 'Device ID and session ID are required'
            }), 400
        
        # Ensure platform is Android
        if platform != 'android':
            return jsonify({
                'success': False,
                'error': 'This endpoint is for Android devices only'
            }), 400
        
        # Start Android recording session
        result = recorder_manager.start_recording_session(device_id, session_id)
        
        if result['success']:
            # Store session info in Flask session
            session['recording_session_id'] = session_id
            session['recording_device_id'] = device_id
            session['recording_platform'] = 'android'
            
            logger.info(f"Started Android recording session {session_id} for device {device_id}")
            
            return jsonify(result)
        else:
            return jsonify(result), 400
            
    except Exception as e:
        logger.error(f"Error starting Android recording session: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500


@recorder_bp.route('/stop', methods=['POST'])
def stop_recording():
    """Stop an active Android recording session"""
    try:
        data = request.get_json()
        session_id = data.get('session_id') or session.get('recording_session_id')
        platform = data.get('platform') or session.get('recording_platform', 'android')
        
        if not session_id:
            return jsonify({
                'success': False,
                'error': 'Session ID is required'
            }), 400
        
        # Stop Android recording session
        result = recorder_manager.stop_recording_session(session_id)
        
        if result['success']:
            logger.info(f"Stopped Android recording session {session_id}")
            return jsonify(result)
        else:
            return jsonify(result), 400
            
    except Exception as e:
        logger.error(f"Error stopping Android recording session: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500


@recorder_bp.route('/capture', methods=['POST'])
def capture_action():
    """Capture a user action during Android recording"""
    try:
        data = request.get_json()
        session_id = data.get('session_id') or session.get('recording_session_id')
        action_data = data.get('action_data')
        platform = data.get('platform') or session.get('recording_platform', 'android')
        
        if not session_id or not action_data:
            return jsonify({
                'success': False,
                'error': 'Session ID and action data are required'
            }), 400
        
        # Ensure platform is Android and add to action data
        action_data['platform'] = 'android'
        
        # Add Android-specific metadata
        if 'timeout' not in action_data:
            action_data['timeout'] = 60  # Android default timeout
        
        # Capture Android action
        result = recorder_manager.capture_action(session_id, action_data)
        
        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 400
            
    except Exception as e:
        logger.error(f"Error capturing Android action: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500


@recorder_bp.route('/save', methods=['POST'])
def save_test_case():
    """Save recorded Android session as a test case"""
    try:
        data = request.get_json()
        session_id = data.get('session_id') or session.get('recording_session_id')
        test_case_name = data.get('test_case_name')
        platform = data.get('platform') or session.get('recording_platform', 'android')
        
        if not session_id or not test_case_name:
            return jsonify({
                'success': False,
                'error': 'Session ID and test case name are required'
            }), 400
        
        # Get test cases directory from settings
        test_case_dir = directory_paths_db.get_path('TEST_CASES')
        
        if not test_case_dir or not os.path.exists(test_case_dir):
            return jsonify({
                'success': False,
                'error': 'Test cases directory not configured or does not exist'
            }), 400
        
        # Save Android test case
        result = recorder_manager.save_test_case(session_id, test_case_name, test_case_dir)
        
        if result['success']:
            # Clear session info
            session.pop('recording_session_id', None)
            session.pop('recording_device_id', None)
            session.pop('recording_platform', None)
            
            logger.info(f"Saved Android test case '{test_case_name}' from session {session_id}")
            return jsonify(result)
        else:
            return jsonify(result), 400
            
    except Exception as e:
        logger.error(f"Error saving Android test case: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500


@recorder_bp.route('/status/<session_id>', methods=['GET'])
def get_session_status(session_id):
    """Get status of an Android recording session"""
    try:
        result = recorder_manager.get_session_status(session_id)
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error getting Android session status: {str(e)}")
        return jsonify({
            'active': False,
            'error': 'Internal server error'
        }), 500


@recorder_bp.route('/cleanup/<session_id>', methods=['POST'])
def cleanup_session(session_id):
    """Clean up an Android recording session"""
    try:
        result = recorder_manager.cleanup_session(session_id)
        
        if result:
            # Clear session info if it matches
            if session.get('recording_session_id') == session_id:
                session.pop('recording_session_id', None)
                session.pop('recording_device_id', None)
                session.pop('recording_platform', None)
            
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'error': 'Android session not found'}), 404
            
    except Exception as e:
        logger.error(f"Error cleaning up Android session: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500


@recorder_bp.route('/sessions', methods=['GET'])
def list_active_sessions():
    """List all active Android recording sessions"""
    try:
        sessions = []
        for session_id in recorder_manager.active_sessions:
            session_info = recorder_manager.get_session_status(session_id)
            if session_info.get('active') and session_info.get('platform') == 'android':
                sessions.append(session_info)
        
        return jsonify({
            'success': True,
            'sessions': sessions,
            'platform': 'android'
        })
        
    except Exception as e:
        logger.error(f"Error listing Android sessions: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500


@recorder_bp.route('/capabilities', methods=['GET'])
def get_recorder_capabilities():
    """Get Android recorder capabilities and supported features"""
    try:
        # Get Android-specific capabilities
        if hasattr(recorder_manager, 'get_android_specific_capabilities'):
            capabilities = recorder_manager.get_android_specific_capabilities()
        else:
            capabilities = {
                'supported_locators': ['id', 'xpath', 'uiselector', 'accessibility_id'],
                'supported_actions': [
                    'tap', 'double_tap', 'long_press', 'swipe', 'text_input',
                    'scroll', 'pinch', 'rotate', 'back_button', 'home_button',
                    'menu_button', 'volume_up', 'volume_down'
                ],
                'platform_features': [
                    'uiautomator2_integration',
                    'webview_context_switching',
                    'native_app_support',
                    'hybrid_app_support'
                ]
            }
        
        capabilities['platform'] = 'android'
        capabilities['version'] = '1.0.0'
        
        return jsonify({
            'success': True,
            'capabilities': capabilities
        })
        
    except Exception as e:
        logger.error(f"Error getting Android capabilities: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500


@recorder_bp.route('/element-identification', methods=['POST'])
def identify_element():
    """Identify Android element at given coordinates using multiple strategies"""
    try:
        data = request.get_json()
        x = data.get('x')
        y = data.get('y')
        page_source = data.get('page_source')
        session_id = data.get('session_id') or session.get('recording_session_id')
        
        if x is None or y is None:
            return jsonify({
                'success': False,
                'error': 'Coordinates (x, y) are required'
            }), 400
        
        # This would integrate with existing Android element identification logic
        # For now, return a placeholder response
        element_info = {
            'success': True,
            'strategies': [
                {
                    'type': 'id',
                    'value': None,
                    'confidence': 0
                },
                {
                    'type': 'xpath',
                    'value': None,
                    'confidence': 0
                },
                {
                    'type': 'uiselector',
                    'value': None,
                    'confidence': 0
                },
                {
                    'type': 'accessibility_id',
                    'value': None,
                    'confidence': 0
                }
            ],
            'coordinates': {'x': x, 'y': y},
            'platform': 'android'
        }
        
        return jsonify(element_info)
        
    except Exception as e:
        logger.error(f"Error identifying Android element: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500


@recorder_bp.route('/context-switch', methods=['POST'])
def switch_context():
    """Switch Android WebView context during recording"""
    try:
        data = request.get_json()
        context = data.get('context', 'NATIVE_APP')
        session_id = data.get('session_id') or session.get('recording_session_id')
        
        if not session_id:
            return jsonify({
                'success': False,
                'error': 'Session ID is required'
            }), 400
        
        # This would integrate with existing Android WebView context switching logic
        # For now, return a placeholder response
        result = {
            'success': True,
            'context': context,
            'available_contexts': ['NATIVE_APP', 'WEBVIEW_1'],
            'platform': 'android'
        }
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error switching Android context: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500


@recorder_bp.errorhandler(404)
def not_found(error):
    """Handle 404 errors"""
    return jsonify({
        'success': False,
        'error': 'Android recorder endpoint not found'
    }), 404


@recorder_bp.errorhandler(405)
def method_not_allowed(error):
    """Handle 405 errors"""
    return jsonify({
        'success': False,
        'error': 'Method not allowed'
    }), 405


@recorder_bp.route('/playback/start', methods=['POST'])
def start_playback():
    """Start playback of a recorded Android test case"""
    try:
        data = request.get_json()
        test_case_data = data.get('test_case_data')
        device_id = data.get('device_id')
        playback_id = data.get('playback_id')
        options = data.get('options', {})

        if not test_case_data or not device_id or not playback_id:
            return jsonify({
                'success': False,
                'error': 'Test case data, device ID, and playback ID are required for Android'
            }), 400

        # Ensure Android platform
        options['platform'] = 'android'

        result = playback_manager.start_playback(playback_id, test_case_data, device_id, options)

        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 400

    except Exception as e:
        logger.error(f"Error starting Android playback: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500


@recorder_bp.route('/playback/execute/<playback_id>', methods=['POST'])
def execute_playback(playback_id):
    """Execute a full Android playback session"""
    try:
        result = playback_manager.execute_playback(playback_id)

        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 400

    except Exception as e:
        logger.error(f"Error executing Android playback: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500


@recorder_bp.route('/playback/execute-action/<playback_id>/<int:action_index>', methods=['POST'])
def execute_single_action(playback_id, action_index):
    """Execute a single Android action from a test case"""
    try:
        result = playback_manager.execute_single_action(playback_id, action_index)

        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 400

    except Exception as e:
        logger.error(f"Error executing single Android action: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500


@recorder_bp.route('/playback/stop/<playback_id>', methods=['POST'])
def stop_playback(playback_id):
    """Stop an active Android playback session"""
    try:
        result = playback_manager.stop_playback(playback_id)

        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 404

    except Exception as e:
        logger.error(f"Error stopping Android playback: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500


@recorder_bp.route('/playback/status/<playback_id>', methods=['GET'])
def get_playback_status(playback_id):
    """Get status of an Android playback session"""
    try:
        result = playback_manager.get_playback_status(playback_id)
        return jsonify(result)

    except Exception as e:
        logger.error(f"Error getting Android playback status: {str(e)}")
        return jsonify({
            'active': False,
            'error': 'Internal server error'
        }), 500


@recorder_bp.route('/playback/capabilities', methods=['GET'])
def get_playback_capabilities():
    """Get Android playback capabilities"""
    try:
        capabilities = playback_manager.get_android_capabilities()
        return jsonify({
            'success': True,
            'capabilities': capabilities
        })

    except Exception as e:
        logger.error(f"Error getting Android playback capabilities: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500


@recorder_bp.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    logger.error(f"Android recorder internal server error: {str(error)}")
    return jsonify({
        'success': False,
        'error': 'Internal server error'
    }), 500
