/**
 * Recorder Manager Module - Android Platform
 * 
 * Manages the test recording functionality for Android devices including session management,
 * device interaction capture, and integration with existing Android systems.
 */

class RecorderManager {
    constructor() {
        this.isRecording = false;
        this.sessionId = null;
        this.deviceId = null;
        this.capturedActions = [];
        this.actionCapture = new ActionCapture();
        this.deviceScreen = null;
        this.actionList = null;
        this.platform = 'android';
        
        this.init();
    }
    
    init() {
        console.log('Initializing Android Recorder Manager');
        this.setupEventListeners();
        this.setupDeviceScreen();
        this.setupActionList();
    }
    
    setupEventListeners() {
        // Recording control buttons
        const recordBtn = document.getElementById('record-btn');
        const stopBtn = document.getElementById('stop-btn');
        const playBtn = document.getElementById('play-btn');
        const saveBtn = document.getElementById('save-btn');
        
        if (recordBtn) {
            recordBtn.addEventListener('click', () => this.startRecording());
        }
        
        if (stopBtn) {
            stopBtn.addEventListener('click', () => this.stopRecording());
        }
        
        if (playBtn) {
            playBtn.addEventListener('click', () => this.playRecording());
        }
        
        if (saveBtn) {
            saveBtn.addEventListener('click', () => this.showSaveModal());
        }
        
        // Save modal events
        const saveModalBtn = document.getElementById('save-modal-btn');
        const cancelModalBtn = document.getElementById('cancel-modal-btn');
        
        if (saveModalBtn) {
            saveModalBtn.addEventListener('click', () => this.saveTestCase());
        }
        
        if (cancelModalBtn) {
            cancelModalBtn.addEventListener('click', () => this.hideSaveModal());
        }
    }
    
    setupDeviceScreen() {
        this.deviceScreen = document.getElementById('device-screen');
        
        if (this.deviceScreen) {
            // Add click event listener for recording interactions
            this.deviceScreen.addEventListener('click', (event) => {
                if (this.isRecording) {
                    this.captureScreenInteraction(event);
                }
            });
            
            // Add other interaction listeners
            this.deviceScreen.addEventListener('dblclick', (event) => {
                if (this.isRecording) {
                    this.captureScreenInteraction(event, 'double_tap');
                }
            });
            
            // Add Android-specific touch events
            this.setupAndroidTouchEvents();
        }
    }
    
    setupAndroidTouchEvents() {
        if (!this.deviceScreen) return;
        
        let touchStartTime = 0;
        let touchStartPos = { x: 0, y: 0 };
        
        this.deviceScreen.addEventListener('touchstart', (event) => {
            if (this.isRecording) {
                touchStartTime = Date.now();
                const touch = event.touches[0];
                const rect = this.deviceScreen.getBoundingClientRect();
                touchStartPos = {
                    x: touch.clientX - rect.left,
                    y: touch.clientY - rect.top
                };
            }
        });
        
        this.deviceScreen.addEventListener('touchend', (event) => {
            if (this.isRecording) {
                const touchDuration = Date.now() - touchStartTime;
                const touch = event.changedTouches[0];
                const rect = this.deviceScreen.getBoundingClientRect();
                const touchEndPos = {
                    x: touch.clientX - rect.left,
                    y: touch.clientY - rect.top
                };
                
                // Determine interaction type based on duration and movement
                if (touchDuration > 500) {
                    this.captureScreenInteraction(event, 'long_press');
                } else {
                    const distance = Math.sqrt(
                        Math.pow(touchEndPos.x - touchStartPos.x, 2) +
                        Math.pow(touchEndPos.y - touchStartPos.y, 2)
                    );
                    
                    if (distance > 20) {
                        this.captureScreenInteraction(event, 'swipe', {
                            start: touchStartPos,
                            end: touchEndPos,
                            duration: touchDuration
                        });
                    } else {
                        this.captureScreenInteraction(event, 'tap');
                    }
                }
            }
        });
        
        // Add Android hardware button simulation
        document.addEventListener('keydown', (event) => {
            if (this.isRecording) {
                this.captureHardwareButtonPress(event);
            }
        });
    }
    
    setupActionList() {
        this.actionList = document.getElementById('action-list');
        this.updateActionCount();
    }
    
    async startRecording() {
        try {
            // Get current Android device ID from session or device selector
            this.deviceId = this.getCurrentDeviceId();
            
            if (!this.deviceId) {
                this.showError('Please connect to an Android device first');
                return;
            }
            
            // Generate session ID
            this.sessionId = this.generateSessionId();
            
            // Start recording session on backend
            const response = await fetch('/api/recorder/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    device_id: this.deviceId,
                    session_id: this.sessionId,
                    platform: 'android'
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.isRecording = true;
                this.capturedActions = [];
                this.updateRecordingUI();
                this.updateActionList();
                console.log('Android recording started successfully');
            } else {
                this.showError(result.error || 'Failed to start Android recording');
            }
            
        } catch (error) {
            console.error('Error starting Android recording:', error);
            this.showError('Failed to start Android recording');
        }
    }
    
    async stopRecording() {
        try {
            if (!this.isRecording || !this.sessionId) {
                return;
            }
            
            // Stop recording session on backend
            const response = await fetch('/api/recorder/stop', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    session_id: this.sessionId,
                    platform: 'android'
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.isRecording = false;
                this.capturedActions = result.actions || [];
                this.updateRecordingUI();
                this.updateActionList();
                console.log('Android recording stopped successfully');
            } else {
                this.showError(result.error || 'Failed to stop Android recording');
            }
            
        } catch (error) {
            console.error('Error stopping Android recording:', error);
            this.showError('Failed to stop Android recording');
        }
    }
    
    async captureScreenInteraction(event, actionType = 'tap', extraData = {}) {
        if (!this.isRecording) return;
        
        const rect = this.deviceScreen.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        
        // Scale coordinates to actual Android device screen size
        const scaledCoords = this.scaleCoordinates(x, y);
        
        const actionData = {
            type: actionType,
            x: scaledCoords.x,
            y: scaledCoords.y,
            timestamp: Date.now(),
            platform: 'android',
            ...extraData
        };
        
        try {
            // Send action to backend for processing
            const response = await fetch('/api/recorder/capture', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    session_id: this.sessionId,
                    action_data: actionData,
                    platform: 'android'
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                // Add to local action list for immediate UI update
                this.capturedActions.push({
                    action_id: result.action_id,
                    type: result.action_type,
                    timestamp: result.timestamp,
                    platform: 'android',
                    ...actionData
                });
                
                this.updateActionList();
                console.log('Android action captured:', result.action_id);
            } else {
                console.error('Failed to capture Android action:', result.error);
            }
            
        } catch (error) {
            console.error('Error capturing Android action:', error);
        }
    }
    
    async captureHardwareButtonPress(event) {
        if (!this.isRecording) return;
        
        let buttonType = null;
        
        switch (event.key) {
            case 'Escape':
                buttonType = 'back_button';
                break;
            case 'Home':
                buttonType = 'home_button';
                break;
            case 'F1':
                buttonType = 'menu_button';
                break;
            case 'VolumeUp':
                buttonType = 'volume_up';
                break;
            case 'VolumeDown':
                buttonType = 'volume_down';
                break;
        }
        
        if (buttonType) {
            const actionData = {
                type: buttonType,
                timestamp: Date.now(),
                platform: 'android'
            };
            
            try {
                const response = await fetch('/api/recorder/capture', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        session_id: this.sessionId,
                        action_data: actionData,
                        platform: 'android'
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    this.capturedActions.push({
                        action_id: result.action_id,
                        type: result.action_type,
                        timestamp: result.timestamp,
                        platform: 'android',
                        ...actionData
                    });
                    
                    this.updateActionList();
                    console.log('Android hardware button captured:', result.action_id);
                }
                
            } catch (error) {
                console.error('Error capturing Android hardware button:', error);
            }
        }
    }
    
    scaleCoordinates(x, y) {
        // Get actual Android device screen dimensions from the image
        const naturalWidth = this.deviceScreen.naturalWidth || this.deviceScreen.width;
        const naturalHeight = this.deviceScreen.naturalHeight || this.deviceScreen.height;
        const displayWidth = this.deviceScreen.width;
        const displayHeight = this.deviceScreen.height;
        
        const scaleX = naturalWidth / displayWidth;
        const scaleY = naturalHeight / displayHeight;
        
        return {
            x: Math.round(x * scaleX),
            y: Math.round(y * scaleY)
        };
    }
    
    updateRecordingUI() {
        const recordBtn = document.getElementById('record-btn');
        const stopBtn = document.getElementById('stop-btn');
        const statusIndicator = document.querySelector('.status-indicator');
        const statusText = document.querySelector('.status-text');
        
        if (this.isRecording) {
            if (recordBtn) {
                recordBtn.classList.add('recording');
                recordBtn.disabled = true;
            }
            if (stopBtn) {
                stopBtn.disabled = false;
            }
            if (statusIndicator) {
                statusIndicator.classList.add('recording');
            }
            if (statusText) {
                statusText.textContent = 'Recording Android...';
            }
            if (this.deviceScreen) {
                this.deviceScreen.classList.add('recording');
            }
        } else {
            if (recordBtn) {
                recordBtn.classList.remove('recording');
                recordBtn.disabled = false;
            }
            if (stopBtn) {
                stopBtn.disabled = true;
            }
            if (statusIndicator) {
                statusIndicator.classList.remove('recording');
                statusIndicator.classList.add('stopped');
            }
            if (statusText) {
                statusText.textContent = 'Stopped';
            }
            if (this.deviceScreen) {
                this.deviceScreen.classList.remove('recording');
            }
        }
    }
    
    updateActionList() {
        if (!this.actionList) return;
        
        this.actionList.innerHTML = '';
        
        this.capturedActions.forEach((action, index) => {
            const actionElement = this.createActionElement(action, index);
            this.actionList.appendChild(actionElement);
        });
        
        this.updateActionCount();
    }
    
    createActionElement(action, index) {
        const actionDiv = document.createElement('div');
        actionDiv.className = 'action-item';
        actionDiv.innerHTML = `
            <div class="action-header">
                <span class="action-type android">${action.type}</span>
                <span class="action-id">${action.action_id || 'N/A'}</span>
            </div>
            <div class="action-details">
                ${this.formatAndroidActionDetails(action)}
            </div>
            <div class="action-controls">
                <button class="action-play-btn" onclick="recorderManager.playAction('${action.action_id}')">
                    Play
                </button>
            </div>
        `;
        
        return actionDiv;
    }
    
    formatAndroidActionDetails(action) {
        let details = [];
        
        if (action.x !== undefined && action.y !== undefined) {
            details.push(`Coordinates: (${action.x}, ${action.y})`);
        }
        
        if (action.text) {
            details.push(`Text: "${action.text}"`);
        }
        
        if (action.locator_type && action.locator_value) {
            details.push(`<div class="locator-info">${action.locator_type}: "${action.locator_value}"</div>`);
        }
        
        details.push(`<small>Platform: Android</small>`);
        
        return details.join('<br>');
    }
    
    updateActionCount() {
        const actionCount = document.querySelector('.action-count');
        if (actionCount) {
            actionCount.textContent = this.capturedActions.length;
        }
    }
    
    getCurrentDeviceId() {
        // Get Android device ID from session storage or device selector
        return localStorage.getItem('currentDeviceId') || 
               sessionStorage.getItem('device_id') ||
               document.getElementById('device-select')?.value;
    }
    
    generateSessionId() {
        return 'android_rec_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    showSaveModal() {
        const modal = document.getElementById('save-modal');
        if (modal) {
            modal.style.display = 'flex';
        }
    }
    
    hideSaveModal() {
        const modal = document.getElementById('save-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }
    
    async saveTestCase() {
        const testCaseName = document.getElementById('test-case-name')?.value;
        
        if (!testCaseName) {
            this.showError('Please enter a test case name');
            return;
        }
        
        try {
            const response = await fetch('/api/recorder/save', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    session_id: this.sessionId,
                    test_case_name: testCaseName,
                    platform: 'android'
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.hideSaveModal();
                this.showSuccess(`Android test case "${testCaseName}" saved successfully`);
                this.resetRecorder();
            } else {
                this.showError(result.error || 'Failed to save Android test case');
            }
            
        } catch (error) {
            console.error('Error saving Android test case:', error);
            this.showError('Failed to save Android test case');
        }
    }
    
    async playRecording() {
        if (this.capturedActions.length === 0) {
            this.showError('No Android actions to play');
            return;
        }
        
        // This would integrate with existing Android test execution system
        console.log('Playing recorded Android actions:', this.capturedActions);
        this.showInfo('Android playback functionality will be integrated with existing test execution system');
    }
    
    async playAction(actionId) {
        const action = this.capturedActions.find(a => a.action_id === actionId);
        if (action) {
            console.log('Playing single Android action:', action);
            this.showInfo('Single Android action playback functionality will be integrated');
        }
    }
    
    resetRecorder() {
        this.isRecording = false;
        this.sessionId = null;
        this.capturedActions = [];
        this.updateRecordingUI();
        this.updateActionList();
    }
    
    showError(message) {
        console.error(message);
        // Integrate with existing notification system
        if (window.showNotification) {
            window.showNotification(message, 'error');
        } else {
            alert('Error: ' + message);
        }
    }
    
    showSuccess(message) {
        console.log(message);
        // Integrate with existing notification system
        if (window.showNotification) {
            window.showNotification(message, 'success');
        } else {
            alert('Success: ' + message);
        }
    }
    
    showInfo(message) {
        console.info(message);
        // Integrate with existing notification system
        if (window.showNotification) {
            window.showNotification(message, 'info');
        } else {
            alert('Info: ' + message);
        }
    }
}

// Initialize Android recorder manager when DOM is loaded
let recorderManager;
document.addEventListener('DOMContentLoaded', () => {
    if (document.getElementById('recorder-tab')) {
        recorderManager = new RecorderManager();
    }
});
