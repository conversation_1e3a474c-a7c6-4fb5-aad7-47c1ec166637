import os
import json
import logging
from pathlib import Path
from cryptography.fernet import Fernet
import base64


class BrowserStackCredentials:
    """
    Utility class for managing BrowserStack credentials with encryption
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.credentials_file = Path(__file__).parent.parent / 'data' / 'browserstack_credentials.json'
        self.key_file = Path(__file__).parent.parent / 'data' / 'browserstack.key'
        
        # Ensure data directory exists
        self.credentials_file.parent.mkdir(exist_ok=True)
        
        # Initialize encryption key
        self._init_encryption_key()
    
    def _init_encryption_key(self):
        """Initialize or load encryption key"""
        try:
            if self.key_file.exists():
                with open(self.key_file, 'rb') as f:
                    self.key = f.read()
            else:
                # Generate new key
                self.key = Fernet.generate_key()
                with open(self.key_file, 'wb') as f:
                    f.write(self.key)
                # Set restrictive permissions
                os.chmod(self.key_file, 0o600)
                
            self.cipher = Fernet(self.key)
        except Exception as e:
            self.logger.error(f"Failed to initialize encryption key: {e}")
            # Fallback to no encryption
            self.cipher = None
    
    def _encrypt_data(self, data):
        """Encrypt sensitive data"""
        if self.cipher and data:
            try:
                return self.cipher.encrypt(data.encode()).decode()
            except Exception as e:
                self.logger.error(f"Failed to encrypt data: {e}")
        return data
    
    def _decrypt_data(self, encrypted_data):
        """Decrypt sensitive data"""
        if self.cipher and encrypted_data:
            try:
                return self.cipher.decrypt(encrypted_data.encode()).decode()
            except Exception as e:
                self.logger.error(f"Failed to decrypt data: {e}")
        return encrypted_data
    
    def save_credentials(self, username, access_key, app_id, remember=False):
        """
        Save BrowserStack credentials
        
        Args:
            username (str): BrowserStack username
            access_key (str): BrowserStack access key
            app_id (str): BrowserStack app ID
            remember (bool): Whether to save credentials persistently
            
        Returns:
            bool: True if saved successfully, False otherwise
        """
        try:
            if not remember:
                # Don't save if remember is False
                return True
                
            credentials = {
                'username': username,
                'access_key': self._encrypt_data(access_key),
                'app_id': app_id,
                'remember': remember
            }
            
            with open(self.credentials_file, 'w') as f:
                json.dump(credentials, f, indent=2)
            
            # Set restrictive permissions
            os.chmod(self.credentials_file, 0o600)
            
            self.logger.info("BrowserStack credentials saved successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save BrowserStack credentials: {e}")
            return False
    
    def load_credentials(self):
        """
        Load saved BrowserStack credentials
        
        Returns:
            dict: Dictionary containing credentials or empty dict if not found
        """
        try:
            if not self.credentials_file.exists():
                return {}
                
            with open(self.credentials_file, 'r') as f:
                credentials = json.load(f)
            
            # Decrypt access key
            if 'access_key' in credentials:
                credentials['access_key'] = self._decrypt_data(credentials['access_key'])
            
            self.logger.info("BrowserStack credentials loaded successfully")
            return credentials
            
        except Exception as e:
            self.logger.error(f"Failed to load BrowserStack credentials: {e}")
            return {}
    
    def clear_credentials(self):
        """
        Clear saved BrowserStack credentials
        
        Returns:
            bool: True if cleared successfully, False otherwise
        """
        try:
            if self.credentials_file.exists():
                self.credentials_file.unlink()
            
            self.logger.info("BrowserStack credentials cleared successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to clear BrowserStack credentials: {e}")
            return False
    
    def validate_credentials(self, username, access_key, app_id):
        """
        Validate BrowserStack credentials format
        
        Args:
            username (str): BrowserStack username
            access_key (str): BrowserStack access key
            app_id (str): BrowserStack app ID
            
        Returns:
            tuple: (is_valid, error_message)
        """
        if not username or not username.strip():
            return False, "Username is required"
        
        if not access_key or not access_key.strip():
            return False, "Access key is required"
        
        if not app_id or not app_id.strip():
            return False, "App ID is required"
        
        # Basic format validation for app ID
        if not app_id.startswith('bs://'):
            return False, "App ID should start with 'bs://'"
        
        if len(app_id) < 10:
            return False, "App ID appears to be too short"
        
        return True, ""
    
    def get_session_credentials(self, session_id):
        """
        Get credentials for a specific session (for multi-session support)
        
        Args:
            session_id (str): Session identifier
            
        Returns:
            dict: Session-specific credentials or empty dict
        """
        try:
            session_file = self.credentials_file.parent / f'browserstack_session_{session_id}.json'
            
            if not session_file.exists():
                return {}
                
            with open(session_file, 'r') as f:
                credentials = json.load(f)
            
            # Decrypt access key
            if 'access_key' in credentials:
                credentials['access_key'] = self._decrypt_data(credentials['access_key'])
            
            return credentials
            
        except Exception as e:
            self.logger.error(f"Failed to load session credentials: {e}")
            return {}
    
    def save_session_credentials(self, session_id, username, access_key, app_id):
        """
        Save credentials for a specific session
        
        Args:
            session_id (str): Session identifier
            username (str): BrowserStack username
            access_key (str): BrowserStack access key
            app_id (str): BrowserStack app ID
            
        Returns:
            bool: True if saved successfully, False otherwise
        """
        try:
            session_file = self.credentials_file.parent / f'browserstack_session_{session_id}.json'
            
            credentials = {
                'username': username,
                'access_key': self._encrypt_data(access_key),
                'app_id': app_id,
                'session_id': session_id
            }
            
            with open(session_file, 'w') as f:
                json.dump(credentials, f, indent=2)
            
            # Set restrictive permissions
            os.chmod(session_file, 0o600)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save session credentials: {e}")
            return False
