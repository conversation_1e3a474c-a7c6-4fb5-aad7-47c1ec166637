"""
Recording Session Manager Module - Android Platform

Manages individual Android recording sessions, tracking captured actions,
session state, and providing Android-specific session functionality.
"""

import time
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)


class RecordingSession:
    """Manages an individual Android recording session"""
    
    def __init__(self, device_id: str, session_id: str):
        self.device_id = device_id
        self.session_id = session_id
        self.start_time = datetime.now()
        self.end_time = None
        self.status = 'recording'
        self.captured_actions: List[Dict[str, Any]] = []
        self.platform = 'android'
        self.metadata = {
            'platform': 'android',
            'session_type': 'recording',
            'version': '1.0.0',
            'android_features': {
                'uiautomator2_support': True,
                'webview_context_switching': True,
                'native_app_support': True,
                'hybrid_app_support': True
            }
        }
        
        logger.info(f"Created Android recording session {session_id} for device {device_id}")
    
    def add_action(self, action: Dict[str, Any]) -> bool:
        """
        Add a captured action to the Android session
        
        Args:
            action: The action data to add
            
        Returns:
            True if action added successfully, False otherwise
        """
        try:
            if self.status != 'recording':
                logger.warning(f"Cannot add action to Android session {self.session_id} - not recording")
                return False
            
            # Add sequence number
            action['sequence'] = len(self.captured_actions) + 1
            
            # Add session metadata
            action['session_id'] = self.session_id
            action['device_id'] = self.device_id
            action['platform'] = 'android'
            
            # Add Android-specific metadata if not present
            if 'timeout' not in action:
                action['timeout'] = 60  # Android default timeout
            
            self.captured_actions.append(action)
            
            logger.debug(f"Added Android action {action.get('action_id')} to session {self.session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding action to Android session: {str(e)}")
            return False
    
    def stop_recording(self) -> bool:
        """
        Stop the Android recording session
        
        Returns:
            True if stopped successfully, False otherwise
        """
        try:
            if self.status != 'recording':
                logger.warning(f"Android session {self.session_id} is not recording")
                return False
            
            self.end_time = datetime.now()
            self.status = 'stopped'
            
            logger.info(f"Stopped Android recording session {self.session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping Android recording session: {str(e)}")
            return False
    
    def get_captured_actions(self) -> List[Dict[str, Any]]:
        """
        Get all captured actions from the Android session
        
        Returns:
            List of captured actions
        """
        return self.captured_actions.copy()
    
    def get_duration(self) -> float:
        """
        Get the duration of the Android recording session in seconds
        
        Returns:
            Duration in seconds
        """
        end_time = self.end_time or datetime.now()
        duration = (end_time - self.start_time).total_seconds()
        return round(duration, 2)
    
    def get_session_info(self) -> Dict[str, Any]:
        """
        Get comprehensive Android session information
        
        Returns:
            Dict containing session details
        """
        return {
            'session_id': self.session_id,
            'device_id': self.device_id,
            'platform': self.platform,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'duration': self.get_duration(),
            'status': self.status,
            'actions_count': len(self.captured_actions),
            'metadata': self.metadata
        }
    
    def remove_action(self, action_id: str) -> bool:
        """
        Remove an action from the Android session
        
        Args:
            action_id: The ID of the action to remove
            
        Returns:
            True if action removed successfully, False otherwise
        """
        try:
            for i, action in enumerate(self.captured_actions):
                if action.get('action_id') == action_id:
                    del self.captured_actions[i]
                    # Update sequence numbers for remaining actions
                    for j, remaining_action in enumerate(self.captured_actions[i:], i):
                        remaining_action['sequence'] = j + 1
                    
                    logger.debug(f"Removed Android action {action_id} from session {self.session_id}")
                    return True
            
            logger.warning(f"Android action {action_id} not found in session {self.session_id}")
            return False
            
        except Exception as e:
            logger.error(f"Error removing action from Android session: {str(e)}")
            return False
    
    def update_action(self, action_id: str, updates: Dict[str, Any]) -> bool:
        """
        Update an existing action in the Android session
        
        Args:
            action_id: The ID of the action to update
            updates: Dict containing fields to update
            
        Returns:
            True if action updated successfully, False otherwise
        """
        try:
            for action in self.captured_actions:
                if action.get('action_id') == action_id:
                    action.update(updates)
                    # Ensure platform is maintained
                    action['platform'] = 'android'
                    logger.debug(f"Updated Android action {action_id} in session {self.session_id}")
                    return True
            
            logger.warning(f"Android action {action_id} not found in session {self.session_id}")
            return False
            
        except Exception as e:
            logger.error(f"Error updating action in Android session: {str(e)}")
            return False
    
    def get_action_by_id(self, action_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a specific action by its ID
        
        Args:
            action_id: The ID of the action to retrieve
            
        Returns:
            Action dict if found, None otherwise
        """
        for action in self.captured_actions:
            if action.get('action_id') == action_id:
                return action.copy()
        return None
    
    def clear_actions(self) -> bool:
        """
        Clear all captured actions from the Android session
        
        Returns:
            True if cleared successfully, False otherwise
        """
        try:
            self.captured_actions.clear()
            logger.info(f"Cleared all actions from Android session {self.session_id}")
            return True
        except Exception as e:
            logger.error(f"Error clearing actions from Android session: {str(e)}")
            return False
    
    def pause_recording(self) -> bool:
        """
        Pause the Android recording session
        
        Returns:
            True if paused successfully, False otherwise
        """
        try:
            if self.status != 'recording':
                logger.warning(f"Cannot pause Android session {self.session_id} - not recording")
                return False
            
            self.status = 'paused'
            logger.info(f"Paused Android recording session {self.session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error pausing Android recording session: {str(e)}")
            return False
    
    def resume_recording(self) -> bool:
        """
        Resume a paused Android recording session
        
        Returns:
            True if resumed successfully, False otherwise
        """
        try:
            if self.status != 'paused':
                logger.warning(f"Cannot resume Android session {self.session_id} - not paused")
                return False
            
            self.status = 'recording'
            logger.info(f"Resumed Android recording session {self.session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error resuming Android recording session: {str(e)}")
            return False
    
    def get_android_specific_info(self) -> Dict[str, Any]:
        """
        Get Android-specific session information
        
        Returns:
            Dict containing Android-specific details
        """
        action_types = {}
        locator_types = {}
        
        for action in self.captured_actions:
            action_type = action.get('type', 'unknown')
            action_types[action_type] = action_types.get(action_type, 0) + 1
            
            locator_type = action.get('locator_type')
            if locator_type:
                locator_types[locator_type] = locator_types.get(locator_type, 0) + 1
        
        return {
            'platform': 'android',
            'action_type_distribution': action_types,
            'locator_type_distribution': locator_types,
            'android_features_used': self.metadata.get('android_features', {}),
            'total_actions': len(self.captured_actions)
        }
    
    def export_session_data(self) -> Dict[str, Any]:
        """
        Export complete Android session data for backup or analysis
        
        Returns:
            Complete session data dict
        """
        return {
            'session_info': self.get_session_info(),
            'android_specific_info': self.get_android_specific_info(),
            'actions': self.get_captured_actions(),
            'export_timestamp': datetime.now().isoformat(),
            'platform': 'android'
        }
