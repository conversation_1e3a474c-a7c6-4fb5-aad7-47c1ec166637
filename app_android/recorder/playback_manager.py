"""
Playback Manager Module - Android Platform

Handles playback of recorded Android test cases with integration to existing test execution engine.
Provides both full test case execution and individual action step execution for debugging.
Includes Android-specific features and optimizations.
"""

import json
import logging
import time
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime

logger = logging.getLogger(__name__)


class PlaybackManager:
    """Manages playback of recorded Android test cases"""
    
    def __init__(self):
        self.active_playbacks: Dict[str, 'PlaybackSession'] = {}
        self.execution_callbacks: Dict[str, Callable] = {}
        self.platform = 'android'
        
    def register_execution_callback(self, action_type: str, callback: Callable) -> None:
        """
        Register a callback function for executing specific Android action types
        
        Args:
            action_type: The action type to handle
            callback: Function to call for execution
        """
        self.execution_callbacks[action_type] = callback
        logger.debug(f"Registered Android execution callback for action type: {action_type}")
    
    def start_playback(self, playback_id: str, test_case_data: Dict[str, Any], 
                      device_id: str, options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Start playback of a recorded Android test case
        
        Args:
            playback_id: Unique identifier for this playback session
            test_case_data: The test case data to execute
            device_id: Target Android device for execution
            options: Optional playback configuration
            
        Returns:
            Dict containing playback status and info
        """
        try:
            if playback_id in self.active_playbacks:
                return {
                    'success': False,
                    'error': 'Android playback session already active'
                }
            
            # Validate test case data for Android
            validation_result = self._validate_android_test_case_data(test_case_data)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'error': f"Invalid Android test case data: {validation_result['error']}"
                }
            
            # Set Android-specific default options
            android_options = self._get_android_default_options()
            android_options.update(options or {})
            
            # Create Android playback session
            playback_session = PlaybackSession(
                playback_id=playback_id,
                test_case_data=test_case_data,
                device_id=device_id,
                options=android_options,
                platform='android'
            )
            
            self.active_playbacks[playback_id] = playback_session
            
            logger.info(f"Started Android playback session {playback_id} for test case '{test_case_data.get('name')}'")
            
            return {
                'success': True,
                'playback_id': playback_id,
                'test_case_name': test_case_data.get('name'),
                'total_actions': len(test_case_data.get('actions', [])),
                'device_id': device_id,
                'platform': 'android',
                'status': 'ready'
            }
            
        except Exception as e:
            logger.error(f"Error starting Android playback: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def execute_playback(self, playback_id: str) -> Dict[str, Any]:
        """
        Execute the full Android playback session
        
        Args:
            playback_id: The playback session identifier
            
        Returns:
            Dict containing execution results
        """
        try:
            if playback_id not in self.active_playbacks:
                return {
                    'success': False,
                    'error': 'Android playback session not found'
                }
            
            playback_session = self.active_playbacks[playback_id]
            actions = playback_session.test_case_data.get('actions', [])
            
            if not actions:
                return {
                    'success': False,
                    'error': 'No Android actions to execute'
                }
            
            playback_session.start_execution()
            
            # Initialize Android-specific execution context
            self._initialize_android_context(playback_session)
            
            results = []
            failed_actions = []
            
            for i, action in enumerate(actions):
                try:
                    # Update current action
                    playback_session.set_current_action(i, action)
                    
                    # Pre-process Android action
                    processed_action = self._preprocess_android_action(action, playback_session)
                    
                    # Execute action
                    action_result = self._execute_android_action(processed_action, playback_session)
                    
                    results.append({
                        'action_index': i,
                        'action_id': action.get('action_id'),
                        'action_type': action.get('type'),
                        'success': action_result['success'],
                        'execution_time': action_result.get('execution_time', 0),
                        'error': action_result.get('error'),
                        'platform': 'android'
                    })
                    
                    if not action_result['success']:
                        failed_actions.append(i)
                        
                        # Check if we should continue on failure
                        if not playback_session.options.get('continue_on_failure', True):
                            logger.warning(f"Stopping Android playback due to failed action {i}")
                            break
                    
                    # Add Android-specific delay between actions
                    delay = playback_session.options.get('action_delay', 1.0)  # Android default: 1 second
                    if delay > 0:
                        time.sleep(delay)
                        
                except Exception as e:
                    logger.error(f"Error executing Android action {i}: {str(e)}")
                    results.append({
                        'action_index': i,
                        'action_id': action.get('action_id'),
                        'action_type': action.get('type'),
                        'success': False,
                        'error': str(e),
                        'platform': 'android'
                    })
                    failed_actions.append(i)
            
            playback_session.complete_execution()
            
            # Calculate summary statistics
            total_actions = len(results)
            successful_actions = sum(1 for r in results if r['success'])
            total_time = playback_session.get_execution_duration()
            
            execution_summary = {
                'success': len(failed_actions) == 0,
                'playback_id': playback_id,
                'platform': 'android',
                'total_actions': total_actions,
                'successful_actions': successful_actions,
                'failed_actions': len(failed_actions),
                'success_rate': successful_actions / total_actions if total_actions > 0 else 0,
                'execution_time': total_time,
                'failed_action_indices': failed_actions,
                'detailed_results': results
            }
            
            logger.info(f"Android playback {playback_id} completed: {successful_actions}/{total_actions} actions successful")
            
            return execution_summary
            
        except Exception as e:
            logger.error(f"Error executing Android playback: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _get_android_default_options(self) -> Dict[str, Any]:
        """
        Get Android-specific default playback options
        
        Returns:
            Dict containing default options
        """
        return {
            'action_delay': 1.0,  # Longer delay for Android
            'continue_on_failure': True,
            'timeout_multiplier': 2.0,  # Android actions may take longer
            'retry_failed_actions': True,
            'max_retries': 3,
            'webview_context_switching': True,
            'uiautomator2_fallback': True
        }
    
    def _validate_android_test_case_data(self, test_case_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate Android test case data before playback
        
        Args:
            test_case_data: The test case data to validate
            
        Returns:
            Dict containing validation result
        """
        try:
            # Basic validation
            if not isinstance(test_case_data, dict):
                return {
                    'valid': False,
                    'error': 'Android test case data must be a dictionary'
                }
            
            if 'actions' not in test_case_data:
                return {
                    'valid': False,
                    'error': 'Android test case data must contain actions'
                }
            
            actions = test_case_data['actions']
            if not isinstance(actions, list) or len(actions) == 0:
                return {
                    'valid': False,
                    'error': 'Android test case must contain at least one action'
                }
            
            # Android-specific validation
            platform = test_case_data.get('metadata', {}).get('platform')
            if platform and platform != 'android':
                logger.warning(f"Test case platform is {platform}, but executing on Android")
            
            # Validate Android action types
            android_action_types = [
                'tap', 'double_tap', 'long_press', 'swipe', 'text_input', 'scroll',
                'pinch', 'rotate', 'back_button', 'home_button', 'menu_button',
                'volume_up', 'volume_down', 'sendKeys', 'pressKey'
            ]
            
            for i, action in enumerate(actions):
                if not isinstance(action, dict):
                    return {
                        'valid': False,
                        'error': f'Android action {i} must be a dictionary'
                    }
                
                action_type = action.get('type')
                if not action_type:
                    return {
                        'valid': False,
                        'error': f'Android action {i} must have a type'
                    }
                
                if action_type not in android_action_types:
                    logger.warning(f"Unknown Android action type: {action_type}")
            
            return {'valid': True}
            
        except Exception as e:
            return {
                'valid': False,
                'error': f'Android validation error: {str(e)}'
            }
    
    def _initialize_android_context(self, playback_session: 'PlaybackSession') -> None:
        """
        Initialize Android-specific execution context
        
        Args:
            playback_session: The playback session
        """
        try:
            # This would initialize Android-specific context
            # such as ensuring proper app context, WebView handling, etc.
            logger.debug(f"Initializing Android context for session {playback_session.playback_id}")
            
        except Exception as e:
            logger.error(f"Error initializing Android context: {str(e)}")
    
    def _preprocess_android_action(self, action: Dict[str, Any], 
                                 playback_session: 'PlaybackSession') -> Dict[str, Any]:
        """
        Preprocess Android action before execution
        
        Args:
            action: The action to preprocess
            playback_session: The playback session context
            
        Returns:
            Preprocessed action
        """
        try:
            processed_action = action.copy()
            
            # Apply Android-specific timeout multiplier
            timeout_multiplier = playback_session.options.get('timeout_multiplier', 2.0)
            if 'timeout' in processed_action:
                processed_action['timeout'] = int(processed_action['timeout'] * timeout_multiplier)
            
            # Ensure Android platform is set
            processed_action['platform'] = 'android'
            
            return processed_action
            
        except Exception as e:
            logger.error(f"Error preprocessing Android action: {str(e)}")
            return action
    
    def _execute_android_action(self, action: Dict[str, Any], 
                              playback_session: 'PlaybackSession') -> Dict[str, Any]:
        """
        Execute a single Android action
        
        Args:
            action: The action to execute
            playback_session: The playback session context
            
        Returns:
            Dict containing execution result
        """
        start_time = time.time()
        
        try:
            action_type = action.get('type')
            
            if action_type in self.execution_callbacks:
                # Use registered Android callback
                callback = self.execution_callbacks[action_type]
                result = callback(action, playback_session)
            else:
                # Use default Android execution
                result = self._default_android_action_execution(action, playback_session)
            
            execution_time = time.time() - start_time
            
            if isinstance(result, dict):
                result['execution_time'] = execution_time
                result['platform'] = 'android'
                return result
            else:
                return {
                    'success': bool(result),
                    'execution_time': execution_time,
                    'platform': 'android'
                }
                
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Error executing Android action {action.get('action_id')}: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'execution_time': execution_time,
                'platform': 'android'
            }
    
    def _default_android_action_execution(self, action: Dict[str, Any], 
                                        playback_session: 'PlaybackSession') -> Dict[str, Any]:
        """
        Default Android action execution that integrates with existing test execution engine
        
        Args:
            action: The action to execute
            playback_session: The playback session context
            
        Returns:
            Dict containing execution result
        """
        try:
            # This would integrate with the existing Android test execution engine
            # For now, simulate execution with Android-specific considerations
            action_type = action.get('type')
            
            logger.debug(f"Executing Android {action_type} action: {action.get('action_id')}")
            
            # Simulate Android execution delay (longer than iOS)
            time.sleep(0.2)
            
            # Handle Android-specific action types
            if action_type in ['back_button', 'home_button', 'menu_button']:
                # Hardware button actions
                return {
                    'success': True,
                    'message': f"Simulated Android hardware button: {action_type}"
                }
            elif action_type in ['volume_up', 'volume_down']:
                # Volume button actions
                return {
                    'success': True,
                    'message': f"Simulated Android volume button: {action_type}"
                }
            else:
                # Standard actions
                return {
                    'success': True,
                    'message': f"Simulated Android execution of {action_type} action"
                }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_android_capabilities(self) -> Dict[str, Any]:
        """
        Get Android-specific playback capabilities
        
        Returns:
            Dict containing Android capabilities
        """
        return {
            'platform': 'android',
            'supported_actions': [
                'tap', 'double_tap', 'long_press', 'swipe', 'text_input',
                'scroll', 'pinch', 'rotate', 'back_button', 'home_button',
                'menu_button', 'volume_up', 'volume_down'
            ],
            'features': [
                'uiautomator2_integration',
                'webview_context_switching',
                'hardware_button_support',
                'retry_mechanism',
                'timeout_adjustment'
            ],
            'default_timeouts': {
                'action_delay': 1.0,
                'timeout_multiplier': 2.0,
                'max_retries': 3
            }
        }


class PlaybackSession:
    """Represents an individual Android playback session"""
    
    def __init__(self, playback_id: str, test_case_data: Dict[str, Any], 
                 device_id: str, options: Dict[str, Any], platform: str = 'android'):
        self.playback_id = playback_id
        self.test_case_data = test_case_data
        self.device_id = device_id
        self.options = options
        self.platform = platform
        self.status = 'ready'
        self.start_time = None
        self.end_time = None
        self.current_action_index = -1
        self.current_action = None
        
    def start_execution(self) -> None:
        """Start the Android execution"""
        self.status = 'executing'
        self.start_time = datetime.now()
        
    def complete_execution(self) -> None:
        """Complete the Android execution"""
        self.status = 'completed'
        self.end_time = datetime.now()
        
    def stop_execution(self) -> None:
        """Stop the Android execution"""
        self.status = 'stopped'
        self.end_time = datetime.now()
        
    def set_current_action(self, index: int, action: Dict[str, Any]) -> None:
        """Set the current Android action being executed"""
        self.current_action_index = index
        self.current_action = action
        
    def get_execution_duration(self) -> float:
        """Get Android execution duration in seconds"""
        if not self.start_time:
            return 0.0
        
        end_time = self.end_time or datetime.now()
        duration = (end_time - self.start_time).total_seconds()
        return round(duration, 2)
