"""
Action Capture Module - Android Platform

Handles the capture and processing of user interactions during Android recording sessions.
Converts raw interaction data into structured action format compatible with the Android test execution engine.
Includes Android-specific features like UISelector support and WebView context handling.
"""

import time
import logging
from typing import Dict, List, Optional, Any, Tuple
from utils.id_generator import generate_action_id

logger = logging.getLogger(__name__)


class ActionCapture:
    """Handles capture and processing of user actions during Android recording"""
    
    def __init__(self):
        self.platform = 'android'
        self.supported_actions = {
            'tap': self._process_tap_action,
            'double_tap': self._process_double_tap_action,
            'long_press': self._process_long_press_action,
            'swipe': self._process_swipe_action,
            'text_input': self._process_text_input_action,
            'scroll': self._process_scroll_action,
            'pinch': self._process_pinch_action,
            'rotate': self._process_rotate_action,
            'back_button': self._process_back_button_action,
            'home_button': self._process_home_button_action,
            'menu_button': self._process_menu_button_action,
            'volume_up': self._process_volume_up_action,
            'volume_down': self._process_volume_down_action
        }
        
        # Android-specific locator strategies
        self.locator_strategies = ['id', 'xpath', 'uiselector', 'accessibility_id']
    
    def process_action(self, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process raw action data into Android-specific structured format
        
        Args:
            action_data: Raw action data from user interaction
            
        Returns:
            Processed action in Android test case format
        """
        try:
            action_type = action_data.get('type', 'unknown')
            
            if action_type not in self.supported_actions:
                logger.warning(f"Unsupported Android action type: {action_type}")
                return self._create_unknown_action(action_data)
            
            # Process the action using the appropriate handler
            processor = self.supported_actions[action_type]
            processed_action = processor(action_data)
            
            # Add common Android fields
            processed_action['action_id'] = generate_action_id()
            processed_action['timestamp'] = int(time.time() * 1000)
            processed_action['platform'] = 'android'
            
            # Add Android-specific timeout and retry settings
            if 'timeout' not in processed_action:
                processed_action['timeout'] = 60  # Android default timeout
            
            logger.debug(f"Processed Android {action_type} action: {processed_action['action_id']}")
            
            return processed_action
            
        except Exception as e:
            logger.error(f"Error processing Android action: {str(e)}")
            return self._create_error_action(action_data, str(e))
    
    def _process_tap_action(self, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process Android tap/click action with multiple locator support"""
        x = action_data.get('x', 0)
        y = action_data.get('y', 0)
        
        # Try to identify element at coordinates using Android-specific methods
        element_info = self._identify_android_element(x, y, action_data.get('page_source'))
        
        action = {
            'type': 'tap',
            'method': 'coordinates',
            'x': x,
            'y': y,
            'timeout': 60,
            'interval': 0.5
        }
        
        # Add element identification if available
        if element_info:
            action.update(element_info)
            if element_info.get('locator_type') and element_info.get('locator_value'):
                action['method'] = 'locator'
        
        return action
    
    def _process_double_tap_action(self, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process Android double tap action"""
        x = action_data.get('x', 0)
        y = action_data.get('y', 0)
        
        element_info = self._identify_android_element(x, y, action_data.get('page_source'))
        
        action = {
            'type': 'doubleTap',
            'method': 'coordinates',
            'x': x,
            'y': y,
            'timeout': 60
        }
        
        if element_info:
            action.update(element_info)
            if element_info.get('locator_type') and element_info.get('locator_value'):
                action['method'] = 'locator'
        
        return action
    
    def _process_long_press_action(self, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process Android long press action"""
        x = action_data.get('x', 0)
        y = action_data.get('y', 0)
        duration = action_data.get('duration', 1000)
        
        element_info = self._identify_android_element(x, y, action_data.get('page_source'))
        
        action = {
            'type': 'longPress',
            'method': 'coordinates',
            'x': x,
            'y': y,
            'duration': duration,
            'timeout': 60
        }
        
        if element_info:
            action.update(element_info)
            if element_info.get('locator_type') and element_info.get('locator_value'):
                action['method'] = 'locator'
        
        return action
    
    def _process_swipe_action(self, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process Android swipe gesture"""
        start_x = action_data.get('start_x', 0)
        start_y = action_data.get('start_y', 0)
        end_x = action_data.get('end_x', 0)
        end_y = action_data.get('end_y', 0)
        duration = action_data.get('duration', 1000)
        
        # Determine swipe direction
        direction = self._calculate_swipe_direction(start_x, start_y, end_x, end_y)
        
        action = {
            'type': 'swipe',
            'direction': direction,
            'start_x': start_x,
            'start_y': start_y,
            'end_x': end_x,
            'end_y': end_y,
            'duration': duration,
            'timeout': 60
        }
        
        return action
    
    def _process_text_input_action(self, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process Android text input action"""
        text = action_data.get('text', '')
        x = action_data.get('x', 0)
        y = action_data.get('y', 0)
        clear_first = action_data.get('clear_first', False)
        
        element_info = self._identify_android_element(x, y, action_data.get('page_source'))
        
        action = {
            'type': 'sendKeys',
            'text': text,
            'clear_first': clear_first,
            'timeout': 60
        }
        
        if element_info:
            action.update(element_info)
        
        return action
    
    def _process_scroll_action(self, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process Android scroll action"""
        direction = action_data.get('direction', 'down')
        distance = action_data.get('distance', 500)
        
        action = {
            'type': 'scroll',
            'direction': direction,
            'distance': distance,
            'timeout': 60
        }
        
        return action
    
    def _process_back_button_action(self, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process Android back button action"""
        return {
            'type': 'pressKey',
            'key': 'BACK',
            'timeout': 30
        }
    
    def _process_home_button_action(self, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process Android home button action"""
        return {
            'type': 'pressKey',
            'key': 'HOME',
            'timeout': 30
        }
    
    def _process_menu_button_action(self, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process Android menu button action"""
        return {
            'type': 'pressKey',
            'key': 'MENU',
            'timeout': 30
        }
    
    def _process_volume_up_action(self, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process Android volume up action"""
        return {
            'type': 'pressKey',
            'key': 'VOLUME_UP',
            'timeout': 30
        }
    
    def _process_volume_down_action(self, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process Android volume down action"""
        return {
            'type': 'pressKey',
            'key': 'VOLUME_DOWN',
            'timeout': 30
        }
    
    def _process_pinch_action(self, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process Android pinch gesture"""
        scale = action_data.get('scale', 1.0)
        center_x = action_data.get('center_x', 0)
        center_y = action_data.get('center_y', 0)
        
        action = {
            'type': 'pinch',
            'scale': scale,
            'center_x': center_x,
            'center_y': center_y,
            'timeout': 60
        }
        
        return action
    
    def _process_rotate_action(self, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process Android rotation gesture"""
        angle = action_data.get('angle', 0)
        center_x = action_data.get('center_x', 0)
        center_y = action_data.get('center_y', 0)
        
        action = {
            'type': 'rotate',
            'angle': angle,
            'center_x': center_x,
            'center_y': center_y,
            'timeout': 60
        }
        
        return action
    
    def _identify_android_element(self, x: int, y: int, page_source: Optional[str] = None) -> Dict[str, Any]:
        """
        Attempt to identify Android element at given coordinates using multiple strategies
        
        Args:
            x: X coordinate
            y: Y coordinate
            page_source: Optional page source for element identification
            
        Returns:
            Dict containing Android element identification info
        """
        element_info = {}
        
        if page_source:
            # Try different Android locator strategies
            
            # 1. Try to find by resource-id
            resource_id = self._extract_resource_id(x, y, page_source)
            if resource_id:
                element_info['locator_type'] = 'id'
                element_info['locator_value'] = resource_id
                return element_info
            
            # 2. Try to find by accessibility-id
            accessibility_id = self._extract_accessibility_id(x, y, page_source)
            if accessibility_id:
                element_info['locator_type'] = 'accessibility_id'
                element_info['locator_value'] = accessibility_id
                return element_info
            
            # 3. Try to generate UISelector
            uiselector = self._generate_uiselector(x, y, page_source)
            if uiselector:
                element_info['locator_type'] = 'uiselector'
                element_info['locator_value'] = uiselector
                return element_info
            
            # 4. Fallback to XPath
            xpath = self._generate_xpath(x, y, page_source)
            if xpath:
                element_info['locator_type'] = 'xpath'
                element_info['locator_value'] = xpath
                return element_info
        
        return element_info
    
    def _extract_resource_id(self, x: int, y: int, page_source: str) -> Optional[str]:
        """Extract resource-id from page source at coordinates"""
        # TODO: Implement actual resource-id extraction logic
        # This would parse the page source XML and find elements at coordinates
        return None
    
    def _extract_accessibility_id(self, x: int, y: int, page_source: str) -> Optional[str]:
        """Extract accessibility-id from page source at coordinates"""
        # TODO: Implement actual accessibility-id extraction logic
        return None
    
    def _generate_uiselector(self, x: int, y: int, page_source: str) -> Optional[str]:
        """Generate UISelector for element at coordinates"""
        # TODO: Implement UISelector generation logic
        # This would analyze the element and create appropriate UISelector syntax
        return None
    
    def _generate_xpath(self, x: int, y: int, page_source: str) -> Optional[str]:
        """Generate XPath for element at coordinates"""
        # TODO: Implement XPath generation logic
        return None
    
    def _calculate_swipe_direction(self, start_x: int, start_y: int, 
                                 end_x: int, end_y: int) -> str:
        """Calculate swipe direction from coordinates"""
        dx = end_x - start_x
        dy = end_y - start_y
        
        if abs(dx) > abs(dy):
            return 'right' if dx > 0 else 'left'
        else:
            return 'down' if dy > 0 else 'up'
    
    def _create_unknown_action(self, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create action for unknown action type"""
        return {
            'type': 'unknown',
            'platform': 'android',
            'raw_data': action_data,
            'error': 'Unsupported Android action type'
        }
    
    def _create_error_action(self, action_data: Dict[str, Any], error: str) -> Dict[str, Any]:
        """Create action for processing errors"""
        return {
            'type': 'error',
            'platform': 'android',
            'raw_data': action_data,
            'error': error,
            'action_id': generate_action_id(),
            'timestamp': int(time.time() * 1000)
        }
