#!/usr/bin/env python3
"""
Complete BrowserStack integration test with the actual test case
"""

import sys
import os
import json
import time
import logging

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_browserstack_with_real_test_case():
    """Test BrowserStack with the actual test case"""
    logger.info("🚀 Starting comprehensive BrowserStack test...")
    
    # BrowserStack credentials
    username = "brianf_WpvDJR"
    access_key = "********************"
    app_id = "bs://d1dcbdead2287c85f4e11c6df63caa6ab98b9397"  # User provided app ID
    
    try:
        from utils.browserstack_device_controller import BrowserStackDeviceController
        from actions.action_factory import ActionFactory
        
        # Create BrowserStack controller
        controller = BrowserStackDeviceController()
        logger.info("✅ BrowserStack controller created")
        
        # Connect to BrowserStack
        logger.info(f"🔗 Connecting to BrowserStack with app ID: {app_id}")
        success = controller.connect_to_device(
            username=username,
            access_key=access_key,
            app_id=app_id,
            platform='ios',
            device_name='iPhone 14 Pro Max',
            os_version='16'
        )
        
        if not success:
            logger.error("❌ Failed to connect to BrowserStack")
            return False
        
        logger.info("✅ Connected to BrowserStack successfully!")
        
        # Create action factory
        action_factory = ActionFactory(controller)
        logger.info("✅ Action factory created")
        
        # Load the test case
        test_case_path = "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/browserstack_test_20250623200218.json"
        
        with open(test_case_path, 'r') as f:
            test_case = json.load(f)
        
        logger.info(f"✅ Loaded test case: {test_case['name']}")
        logger.info(f"📋 Test has {len(test_case['actions'])} actions")
        
        # Execute each action
        for i, action in enumerate(test_case['actions'], 1):
            action_type = action.get('type')
            action_id = action.get('action_id', f'action_{i}')
            
            logger.info(f"🎯 Executing action {i}/{len(test_case['actions'])}: {action_type} (ID: {action_id})")
            
            try:
                # Execute the action
                result = action_factory.execute_action(action_type, action)
                
                if result.get('status') == 'success':
                    logger.info(f"✅ Action {i} ({action_type}) succeeded: {result.get('message', '')}")
                else:
                    logger.error(f"❌ Action {i} ({action_type}) failed: {result.get('message', '')}")
                    return False
                
                # Add a small delay between actions
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"❌ Action {i} ({action_type}) crashed: {e}")
                return False
        
        logger.info("🎉 All test actions completed successfully!")
        
        # Disconnect
        controller.disconnect()
        logger.info("✅ Disconnected from BrowserStack")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_individual_actions():
    """Test individual action types with BrowserStack"""
    logger.info("🧪 Testing individual action types...")
    
    username = "brianf_WpvDJR"
    access_key = "********************"
    app_id = "bs://d1dcbdead2287c85f4e11c6df63caa6ab98b9397"
    
    try:
        from utils.browserstack_device_controller import BrowserStackDeviceController
        from actions.action_factory import ActionFactory
        
        controller = BrowserStackDeviceController()
        
        # Connect
        success = controller.connect_to_device(
            username=username,
            access_key=access_key,
            app_id=app_id,
            platform='ios',
            device_name='iPhone 14 Pro Max',
            os_version='16'
        )
        
        if not success:
            logger.error("❌ Failed to connect for individual tests")
            return False
        
        action_factory = ActionFactory(controller)
        
        # Test individual methods
        tests = [
            # Test restart app
            {
                'name': 'Restart App',
                'type': 'restartApp',
                'params': {'package_id': 'com.saucelabs.SwagLabsMobileApp'}
            },
            # Test tap element
            {
                'name': 'Tap Element',
                'type': 'tap',
                'params': {
                    'method': 'locator',
                    'locator_type': 'xpath',
                    'locator_value': '//XCUIElementTypeTextField[@name="test-Username"]',
                    'timeout': 10
                }
            },
            # Test text input
            {
                'name': 'Text Input',
                'type': 'text',
                'params': {'text': 'test_user'}
            },
            # Test exists check
            {
                'name': 'Check Exists',
                'type': 'exists',
                'params': {
                    'locator_type': 'xpath',
                    'locator_value': '//XCUIElementTypeTextField[@name="test-Username"]',
                    'timeout': 10
                }
            }
        ]
        
        for test in tests:
            logger.info(f"🧪 Testing {test['name']}...")
            try:
                result = action_factory.execute_action(test['type'], test['params'])
                if result.get('status') == 'success':
                    logger.info(f"✅ {test['name']} passed")
                else:
                    logger.warning(f"⚠️ {test['name']} failed: {result.get('message', '')}")
            except Exception as e:
                logger.error(f"❌ {test['name']} crashed: {e}")
            
            time.sleep(2)  # Wait between tests
        
        controller.disconnect()
        return True
        
    except Exception as e:
        logger.error(f"❌ Individual tests failed: {e}")
        return False

def main():
    """Run all tests"""
    logger.info("🎯 Starting BrowserStack comprehensive testing...")
    
    # Test 1: Individual action types
    logger.info("\n" + "="*50)
    logger.info("TEST 1: Individual Action Types")
    logger.info("="*50)
    
    individual_success = test_individual_actions()
    
    # Test 2: Complete test case
    logger.info("\n" + "="*50)
    logger.info("TEST 2: Complete Test Case Execution")
    logger.info("="*50)
    
    complete_success = test_browserstack_with_real_test_case()
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("TEST SUMMARY")
    logger.info("="*50)
    
    logger.info(f"Individual Actions Test: {'✅ PASSED' if individual_success else '❌ FAILED'}")
    logger.info(f"Complete Test Case: {'✅ PASSED' if complete_success else '❌ FAILED'}")
    
    if individual_success and complete_success:
        logger.info("🎉 ALL TESTS PASSED! BrowserStack integration is working perfectly!")
        return 0
    else:
        logger.error("❌ Some tests failed. Check the logs above for details.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
