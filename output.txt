2025-07-08 19:02:30,935 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-08 19:02:30,935 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-08 19:02:30,936 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/test_cases
2025-07-08 19:02:30,936 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/reports
2025-07-08 19:02:30,936 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
2025-07-08 19:02:30,937 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/reference_images
2025-07-08 19:02:30,937 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/test_suites
2025-07-08 19:02:30,937 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/suites
2025-07-08 19:02:30,938 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings
2025-07-08 19:02:30,938 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp
2025-07-08 19:02:30,938 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
2025-07-08 19:02:30,938 - __main__ - INFO - Using default ports - killing existing processes to avoid conflicts
2025-07-08 19:02:30,938 - __main__ - INFO - Killing any existing Appium and iproxy processes...
2025-07-08 19:02:32,985 - __main__ - INFO - Existing processes terminated
2025-07-08 19:02:35,135 - utils.global_values_db - INFO - Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values.db
2025-07-08 19:02:35,136 - utils.global_values_db - INFO - Global values database initialized successfully
2025-07-08 19:02:35,136 - utils.global_values_db - INFO - Using global values from config.py
2025-07-08 19:02:35,136 - utils.global_values_db - INFO - Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 2, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
2025-07-08 19:02:35,138 - utils.healenium_config - INFO - Loaded Healenium configuration: enabled=True
2025-07-08 19:02:35,138 - appium_device_controller - WARNING - TouchAction not available in this Appium Python Client version - using W3C Actions fallback
2025-07-08 19:02:35,185 - AppiumDeviceController - INFO - Successfully imported Airtest library.
2025-07-08 19:02:35,831 - utils.database - INFO - === UPDATING TEST_STEPS TABLE SCHEMA ===
2025-07-08 19:02:35,831 - utils.database - INFO - Test_steps table schema updated successfully
2025-07-08 19:02:35,831 - utils.database - INFO - === UPDATING SCREENSHOTS TABLE SCHEMA ===
2025-07-08 19:02:35,832 - utils.database - INFO - Screenshots table schema updated successfully
2025-07-08 19:02:35,832 - utils.database - INFO - === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
2025-07-08 19:02:35,832 - utils.database - INFO - step_idx column already exists in execution_tracking table
2025-07-08 19:02:35,832 - utils.database - INFO - action_type column already exists in execution_tracking table
2025-07-08 19:02:35,832 - utils.database - INFO - action_params column already exists in execution_tracking table
2025-07-08 19:02:35,832 - utils.database - INFO - action_id column already exists in execution_tracking table
2025-07-08 19:02:35,832 - utils.database - INFO - Successfully updated execution_tracking table schema
2025-07-08 19:02:35,832 - utils.database - INFO - Database initialized successfully
2025-07-08 19:02:35,832 - utils.database - INFO - Checking initial database state...
2025-07-08 19:02:35,834 - utils.database - INFO - Database state: 0 suites, 0 cases, 8223 steps, 0 screenshots, 0 tracking entries
2025-07-08 19:02:35,875 - app - INFO - Using directories from config.py:
2025-07-08 19:02:35,875 - app - INFO -   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/test_cases
2025-07-08 19:02:35,875 - app - INFO -   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/reference_images
2025-07-08 19:02:35,875 - app - INFO -   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
[2025-07-08 19:02:36,013] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-07-08 19:02:36,037] WARNING in appium_device_controller: Appium server check failed: HTTPConnectionPool(host='127.0.0.1', port=4723): Max retries exceeded with url: /wd/hub/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x11f4641a0>: Failed to establish a new connection: [Errno 61] Connection refused'))
[2025-07-08 19:02:36,037] INFO in appium_device_controller: Checking for existing Appium and iproxy processes...
[2025-07-08 19:02:36,067] INFO in appium_device_controller: Attempted to kill Appium processes
[2025-07-08 19:02:36,094] INFO in appium_device_controller: Attempted to kill iproxy processes (default ports only)
[2025-07-08 19:02:38,101] INFO in appium_device_controller: No Appium server detected. Starting a new one...
[2025-07-08 19:02:38,103] INFO in appium_device_controller: Using local Appium installation at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium
[2025-07-08 19:02:39,466] INFO in appium_device_controller: Installed Appium drivers: 
[2025-07-08 19:02:39,466] INFO in appium_device_controller: Installing XCUITest driver...
[31mError: ✖ A driver named "xcuitest" is already installed. Did you mean to update? Run "appium driver update". See installed drivers with "appium driver list --installed".[39m
[2025-07-08 19:02:40,214] ERROR in appium_device_controller: Error checking/installing drivers: Command '['/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium', 'driver', 'install', 'xcuitest']' returned non-zero exit status 1.
[2025-07-08 19:02:40,214] INFO in appium_device_controller: Enabling inspector plugin if available
[2025-07-08 19:02:40,214] INFO in appium_device_controller: Appium server output will be logged to: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/appium_server.log
[2025-07-08 19:02:40,220] INFO in appium_device_controller: Started Appium server using command: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium --base-path /wd/hub --port 4723 --relaxed-security --use-drivers xcuitest,uiautomator2 --use-plugins=inspector --session-override --allow-cors --allow-insecure chromedriver_autodownload
[2025-07-08 19:02:42,223] WARNING in appium_device_controller: Waiting for Appium server to start (attempt 1/15): HTTPConnectionPool(host='127.0.0.1', port=4723): Max retries exceeded with url: /wd/hub/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x11f461e50>: Failed to establish a new connection: [Errno 61] Connection refused'))
[2025-07-08 19:02:44,237] INFO in appium_device_controller: Appium server started successfully
[2025-07-08 19:02:44,237] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '97cfc553a41517b332064de3949071c6d64039d7', 'built': '2025-07-07 20:23:08 +1000'}}}
[2025-07-08 19:02:46,241] INFO in appium_device_controller: Appium server started successfully
[2025-07-08 19:02:46,241] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '97cfc553a41517b332064de3949071c6d64039d7', 'built': '2025-07-07 20:23:08 +1000'}}}
[2025-07-08 19:02:48,251] INFO in appium_device_controller: Appium server started successfully
[2025-07-08 19:02:48,251] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '97cfc553a41517b332064de3949071c6d64039d7', 'built': '2025-07-07 20:23:08 +1000'}}}
[2025-07-08 19:02:50,258] INFO in appium_device_controller: Appium server started successfully
[2025-07-08 19:02:50,258] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '97cfc553a41517b332064de3949071c6d64039d7', 'built': '2025-07-07 20:23:08 +1000'}}}
[2025-07-08 19:02:52,264] INFO in appium_device_controller: Appium server started successfully
[2025-07-08 19:02:52,264] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '97cfc553a41517b332064de3949071c6d64039d7', 'built': '2025-07-07 20:23:08 +1000'}}}
[2025-07-08 19:02:54,268] INFO in appium_device_controller: Appium server started successfully
[2025-07-08 19:02:54,268] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '97cfc553a41517b332064de3949071c6d64039d7', 'built': '2025-07-07 20:23:08 +1000'}}}
[2025-07-08 19:02:56,276] INFO in appium_device_controller: Appium server started successfully
[2025-07-08 19:02:56,276] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '97cfc553a41517b332064de3949071c6d64039d7', 'built': '2025-07-07 20:23:08 +1000'}}}
[2025-07-08 19:02:58,288] INFO in appium_device_controller: Appium server started successfully
[2025-07-08 19:02:58,288] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '97cfc553a41517b332064de3949071c6d64039d7', 'built': '2025-07-07 20:23:08 +1000'}}}
[2025-07-08 19:03:00,297] INFO in appium_device_controller: Appium server started successfully
[2025-07-08 19:03:00,298] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '97cfc553a41517b332064de3949071c6d64039d7', 'built': '2025-07-07 20:23:08 +1000'}}}
[2025-07-08 19:03:02,302] INFO in appium_device_controller: Appium server started successfully
[2025-07-08 19:03:02,302] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '97cfc553a41517b332064de3949071c6d64039d7', 'built': '2025-07-07 20:23:08 +1000'}}}
[2025-07-08 19:03:04,308] INFO in appium_device_controller: Appium server started successfully
[2025-07-08 19:03:04,308] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '97cfc553a41517b332064de3949071c6d64039d7', 'built': '2025-07-07 20:23:08 +1000'}}}
[2025-07-08 19:03:06,313] INFO in appium_device_controller: Appium server started successfully
[2025-07-08 19:03:06,313] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '97cfc553a41517b332064de3949071c6d64039d7', 'built': '2025-07-07 20:23:08 +1000'}}}
[2025-07-08 19:03:08,318] INFO in appium_device_controller: Appium server started successfully
[2025-07-08 19:03:08,318] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '97cfc553a41517b332064de3949071c6d64039d7', 'built': '2025-07-07 20:23:08 +1000'}}}
[2025-07-08 19:03:10,327] INFO in appium_device_controller: Appium server started successfully
[2025-07-08 19:03:10,327] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '97cfc553a41517b332064de3949071c6d64039d7', 'built': '2025-07-07 20:23:08 +1000'}}}
Starting Mobile App Automation Tool...
Configuration:
  - Flask server port: 8080
  - Appium server port: 4723
  - WebDriverAgent port: 8100
Open your web browser and navigate to: http://localhost:8080
 * Serving Flask app 'app'
 * Debug mode: on
[2025-07-08 19:03:10,361] INFO in _internal: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8080
 * Running on http://************:8080
[2025-07-08 19:03:10,362] INFO in _internal: [33mPress CTRL+C to quit[0m
[2025-07-08 19:03:21,955] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:21] "GET / HTTP/1.1" 200 -
[2025-07-08 19:03:22,122] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/css/test-suites-styles.css HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,125] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/css/test-cases-styles.css HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,126] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/css/modern-styles.css HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,128] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/css/execution-overlay.css HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,129] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,130] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/css/fixed-device-screen.css HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,130] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/css/test-case.css HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,131] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/css/actionStyles.css HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,133] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/css/test-case-modification.css HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,136] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/css/recorder.css HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,137] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/js/modules/actionFormManager.js HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,138] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/js/modules/uiUtils.js HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,139] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/js/modules/reportAndFormUtils.js HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,141] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/js/export-run.js HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,142] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/img/no_device.png HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,146] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /screenshot?t=0 HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,150] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "GET /static/js/action-manager.js?v=1751968401 HTTP/1.1" 200 -
[2025-07-08 19:03:22,155] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/js/utils.js HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,158] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/js/modules/ElementInteractions.js HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,158] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/js/fixed-device-screen.js HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,160] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/js/execution-manager.js HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,160] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/js/execution-overlay.js HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,163] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/js/modules/TestCaseManager.js HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,164] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/js/modules/ActionCapture.js HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,167] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/js/modules/RecorderManager.js HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,168] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/js/action-description.js HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,171] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/js/multi-step-action.js HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,172] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/js/repeat-steps-action.js HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,175] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/js/modules/tap-fallback-manager.js HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,175] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/js/hook-action.js HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,176] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/js/modules/fallback-locators.js HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,177] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/js/random-data-generator.js HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,180] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "GET /static/js/main.js?v=1751968401 HTTP/1.1" 200 -
[2025-07-08 19:03:22,181] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/js/test_suites.js HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,184] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/js/environment-variables.js HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,184] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/js/settings.js HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,188] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/js/test-case-modification.js HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,190] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/js/import-export.js HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,214] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/guide/faq_guide.html HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,238] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-07-08 19:03:22,242] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "GET /api/environments HTTP/1.1" 200 -
[2025-07-08 19:03:22,244] INFO in directory_paths_db: Directory paths and environments database initialized/verified
[2025-07-08 19:03:22,249] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-07-08 19:03:22,250] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:03:22,255] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-08 19:03:22,266] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:03:22,270] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "GET /api/environment_variables HTTP/1.1" 200 -
[2025-07-08 19:03:22,271] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "GET /api/settings HTTP/1.1" 200 -
[2025-07-08 19:03:22,280] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-08 19:03:22,284] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "GET /api/environments/current HTTP/1.1" 200 -
[2025-07-08 19:03:22,294] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-08 19:03:22,303] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "GET /api/test_suites/list HTTP/1.1" 200 -
[2025-07-08 19:03:22,308] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "[36mGET /static/img/favicon.ico HTTP/1.1[0m" 304 -
[2025-07-08 19:03:22,315] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-08 19:03:22,321] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "GET /api/environments/5/variables HTTP/1.1" 200 -
[2025-07-08 19:03:22,326] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-08 19:03:22,334] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-08 19:03:22,359] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "GET /api/test_cases/action_types HTTP/1.1" 200 -
[2025-07-08 19:03:22,428] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "GET /api/recording/list HTTP/1.1" 200 -
[2025-07-08 19:03:22,430] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-07-08 19:03:22,432] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "GET /api/tools/scan-databases HTTP/1.1" 200 -
[2025-07-08 19:03:22,437] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "GET /api/test_cases/locator_types HTTP/1.1" 200 -
[2025-07-08 19:03:22,470] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:22] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-07-08 19:03:24,005] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-07-08 19:03:24,011] INFO in appium_device_controller: Appium server is running and ready
[2025-07-08 19:03:24,011] INFO in appium_device_controller: Appium server is already running and responsive
[2025-07-08 19:03:24,012] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:24] "GET /api/devices HTTP/1.1" 200 -
[2025-07-08 19:03:26,145] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-07-08 19:03:26,149] INFO in appium_device_controller: Appium server is running and ready
[2025-07-08 19:03:26,149] INFO in appium_device_controller: Appium server is already running and responsive
[2025-07-08 19:03:26,149] INFO in appium_device_controller: Connecting to device: 00008120-00186C801E13C01E with options: None, platform hint: iOS
[2025-07-08 19:03:26,149] INFO in appium_device_controller: Connection attempt 1/3
[2025-07-08 19:03:26,149] INFO in appium_device_controller: Using provided platform hint: iOS
[2025-07-08 19:03:26,149] INFO in appium_device_controller: Using custom WebDriverAgent URL: http://localhost:8100
[2025-07-08 19:03:26,149] INFO in appium_device_controller: Desired capabilities: {'platformName': 'iOS', 'deviceName': '00008120-00186C801E13C01E', 'udid': '00008120-00186C801E13C01E', 'newCommandTimeout': 300, 'noReset': True, 'automationName': 'XCUITest', 'xcodeOrgId': '', 'xcodeSigningId': 'iPhone Developer', 'webDriverAgentUrl': 'http://localhost:8100', 'showIOSLog': True}
[2025-07-08 19:03:26,149] INFO in appium_device_controller: Connecting to Appium server with options: {'platformName': 'iOS', 'appium:deviceName': '00008120-00186C801E13C01E', 'appium:udid': '00008120-00186C801E13C01E', 'appium:newCommandTimeout': 300, 'appium:noReset': True, 'appium:automationName': 'XCUITest', 'appium:xcodeOrgId': '', 'appium:xcodeSigningId': 'iPhone Developer', 'appium:webDriverAgentUrl': 'http://localhost:8100', 'appium:showIOSLog': True}
[2025-07-08 19:03:26,149] INFO in appium_device_controller: Connecting to iOS device via WebDriverAgent
[2025-07-08 19:03:26,164] INFO in appium_device_controller: Device 00008120-00186C801E13C01E is listed and trusted
[2025-07-08 19:03:26,164] INFO in appium_device_controller: Found port 8100 for device 00008120-00186C801E13C01E in wda_ports.txt
[2025-07-08 19:03:26,164] INFO in appium_device_controller: Using WebDriverAgent URL: http://localhost:8100 for device 00008120-00186C801E13C01E
[2025-07-08 19:03:26,167] INFO in appium_device_controller: WebDriverAgent not detected at http://localhost:8100, will try to start port forwarding: HTTPConnectionPool(host='localhost', port=8100): Max retries exceeded with url: /status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x11f447ce0>: Failed to establish a new connection: [Errno 61] Connection refused'))
[2025-07-08 19:03:27,238] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:03:27,240] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:27] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:03:27,242] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:03:27,243] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:27] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:03:27,262] INFO in appium_device_controller: Using tidevice for port forwarding: 8100 -> 8100
[2025-07-08 19:03:29,273] INFO in appium_device_controller: tidevice port forwarding started successfully
[2025-07-08 19:03:29,274] INFO in appium_device_controller: Checking WebDriverAgent connection (attempt 1/5)...
[2025-07-08 19:03:29,280] WARNING in appium_device_controller: WebDriverAgent connection attempt 1 failed: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
[2025-07-08 19:03:32,239] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:03:32,240] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:32] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:03:32,243] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:03:32,244] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:32] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:03:32,284] INFO in appium_device_controller: Checking WebDriverAgent connection (attempt 2/5)...
[2025-07-08 19:03:32,290] WARNING in appium_device_controller: WebDriverAgent connection attempt 2 failed: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
[2025-07-08 19:03:35,294] INFO in appium_device_controller: Checking WebDriverAgent connection (attempt 3/5)...
[2025-07-08 19:03:35,298] WARNING in appium_device_controller: WebDriverAgent connection attempt 3 failed: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
[2025-07-08 19:03:37,235] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:03:37,236] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:37] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:03:37,238] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:03:37,239] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:37] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:03:38,299] INFO in appium_device_controller: Checking WebDriverAgent connection (attempt 4/5)...
[2025-07-08 19:03:38,303] WARNING in appium_device_controller: WebDriverAgent connection attempt 4 failed: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
[2025-07-08 19:03:41,306] INFO in appium_device_controller: Checking WebDriverAgent connection (attempt 5/5)...
[2025-07-08 19:03:41,310] WARNING in appium_device_controller: WebDriverAgent connection attempt 5 failed: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
[2025-07-08 19:03:41,310] ERROR in appium_device_controller: WebDriverAgent is not running at http://localhost:8100 after 5 attempts
[2025-07-08 19:03:41,310] INFO in appium_device_controller: Attempting to connect with Airtest as fallback for iOS
[2025-07-08 19:03:41,310] INFO in appium_device_controller: Initializing Airtest connection for device: 00008120-00186C801E13C01E...
[2025-07-08 19:03:41,313] INFO in appium_device_controller: Connecting to iOS device with WebDriverAgent at http://localhost:8100
[2025-07-08 19:03:41,317] ERROR in ios_device: Error getting screen dimensions: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
[2025-07-08 19:03:41,317] INFO in ios_device: Initialized MinimalIOSDevice for 00008120-00186C801E13C01E with WDA at http://localhost:8100
[2025-07-08 19:03:41,321] ERROR in ios_device: Error getting screen dimensions: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
[2025-07-08 19:03:41,321] ERROR in appium_device_controller: Failed to get screen resolution from iOS device
[2025-07-08 19:03:41,321] ERROR in appium_device_controller: Failed to connect with both Appium and Airtest
[2025-07-08 19:03:41,321] WARNING in appium_device_controller: Connection attempt 1 failed, will retry
[2025-07-08 19:03:42,235] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:03:42,236] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:42] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:03:42,239] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:03:42,239] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:42] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:03:43,326] INFO in appium_device_controller: Connection attempt 2/3
[2025-07-08 19:03:43,326] INFO in appium_device_controller: Using provided platform hint: iOS
[2025-07-08 19:03:43,326] INFO in appium_device_controller: Using custom WebDriverAgent URL: http://localhost:8100
[2025-07-08 19:03:43,326] INFO in appium_device_controller: Desired capabilities: {'platformName': 'iOS', 'deviceName': '00008120-00186C801E13C01E', 'udid': '00008120-00186C801E13C01E', 'newCommandTimeout': 300, 'noReset': True, 'automationName': 'XCUITest', 'xcodeOrgId': '', 'xcodeSigningId': 'iPhone Developer', 'webDriverAgentUrl': 'http://localhost:8100', 'showIOSLog': True}
[2025-07-08 19:03:43,326] INFO in appium_device_controller: Connecting to Appium server with options: {'platformName': 'iOS', 'appium:deviceName': '00008120-00186C801E13C01E', 'appium:udid': '00008120-00186C801E13C01E', 'appium:newCommandTimeout': 300, 'appium:noReset': True, 'appium:automationName': 'XCUITest', 'appium:xcodeOrgId': '', 'appium:xcodeSigningId': 'iPhone Developer', 'appium:webDriverAgentUrl': 'http://localhost:8100', 'appium:showIOSLog': True}
[2025-07-08 19:03:43,326] INFO in appium_device_controller: Connecting to iOS device via WebDriverAgent
[2025-07-08 19:03:43,341] INFO in appium_device_controller: Device 00008120-00186C801E13C01E is listed and trusted
[2025-07-08 19:03:43,342] INFO in appium_device_controller: Found port 8100 for device 00008120-00186C801E13C01E in wda_ports.txt
[2025-07-08 19:03:43,342] INFO in appium_device_controller: Using WebDriverAgent URL: http://localhost:8100 for device 00008120-00186C801E13C01E
[2025-07-08 19:03:43,347] INFO in appium_device_controller: WebDriverAgent not detected at http://localhost:8100, will try to start port forwarding: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
[2025-07-08 19:03:43,348] INFO in appium_device_controller: Port 8100 is already in use, checking if WebDriverAgent is responding...
[2025-07-08 19:03:43,351] WARNING in appium_device_controller: Port 8100 is in use but WebDriverAgent is not responding: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer')). Will restart.
[2025-07-08 19:03:43,523] INFO in appium_device_controller: Killed process 3108 using port 8100
[2025-07-08 19:03:44,634] INFO in appium_device_controller: Using tidevice for port forwarding: 8100 -> 8100
[2025-07-08 19:03:46,642] INFO in appium_device_controller: tidevice port forwarding started successfully
[2025-07-08 19:03:46,642] INFO in appium_device_controller: Checking WebDriverAgent connection (attempt 1/5)...
[2025-07-08 19:03:46,648] WARNING in appium_device_controller: WebDriverAgent connection attempt 1 failed: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
[2025-07-08 19:03:47,237] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:03:47,238] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:47] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:03:47,240] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:03:47,241] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:47] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:03:49,649] INFO in appium_device_controller: Checking WebDriverAgent connection (attempt 2/5)...
[2025-07-08 19:03:49,653] WARNING in appium_device_controller: WebDriverAgent connection attempt 2 failed: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
[2025-07-08 19:03:52,235] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:03:52,236] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:52] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:03:52,238] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:03:52,239] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:52] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:03:52,657] INFO in appium_device_controller: Checking WebDriverAgent connection (attempt 3/5)...
[2025-07-08 19:03:52,661] WARNING in appium_device_controller: WebDriverAgent connection attempt 3 failed: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
[2025-07-08 19:03:55,661] INFO in appium_device_controller: Checking WebDriverAgent connection (attempt 4/5)...
[2025-07-08 19:03:55,665] WARNING in appium_device_controller: WebDriverAgent connection attempt 4 failed: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
[2025-07-08 19:03:57,235] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:03:57,236] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:57] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:03:57,238] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:03:57,239] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:03:57] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:03:58,665] INFO in appium_device_controller: Checking WebDriverAgent connection (attempt 5/5)...
[2025-07-08 19:03:58,669] WARNING in appium_device_controller: WebDriverAgent connection attempt 5 failed: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
[2025-07-08 19:03:58,669] ERROR in appium_device_controller: WebDriverAgent is not running at http://localhost:8100 after 5 attempts
[2025-07-08 19:03:58,669] INFO in appium_device_controller: Attempting to connect with Airtest as fallback for iOS
[2025-07-08 19:03:58,669] INFO in appium_device_controller: Initializing Airtest connection for device: 00008120-00186C801E13C01E...
[2025-07-08 19:03:58,669] INFO in appium_device_controller: Connecting to iOS device with WebDriverAgent at http://localhost:8100
[2025-07-08 19:03:58,672] ERROR in ios_device: Error getting screen dimensions: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
[2025-07-08 19:03:58,672] INFO in ios_device: Initialized MinimalIOSDevice for 00008120-00186C801E13C01E with WDA at http://localhost:8100
[2025-07-08 19:03:58,674] ERROR in ios_device: Error getting screen dimensions: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
[2025-07-08 19:03:58,675] ERROR in appium_device_controller: Failed to get screen resolution from iOS device
[2025-07-08 19:03:58,675] ERROR in appium_device_controller: Failed to connect with both Appium and Airtest
[2025-07-08 19:03:58,675] WARNING in appium_device_controller: Connection attempt 2 failed, will retry
[2025-07-08 19:04:00,678] INFO in appium_device_controller: Connection attempt 3/3
[2025-07-08 19:04:00,678] INFO in appium_device_controller: Using provided platform hint: iOS
[2025-07-08 19:04:00,678] INFO in appium_device_controller: Using custom WebDriverAgent URL: http://localhost:8100
[2025-07-08 19:04:00,678] INFO in appium_device_controller: Desired capabilities: {'platformName': 'iOS', 'deviceName': '00008120-00186C801E13C01E', 'udid': '00008120-00186C801E13C01E', 'newCommandTimeout': 300, 'noReset': True, 'automationName': 'XCUITest', 'xcodeOrgId': '', 'xcodeSigningId': 'iPhone Developer', 'webDriverAgentUrl': 'http://localhost:8100', 'showIOSLog': True}
[2025-07-08 19:04:00,678] INFO in appium_device_controller: Connecting to Appium server with options: {'platformName': 'iOS', 'appium:deviceName': '00008120-00186C801E13C01E', 'appium:udid': '00008120-00186C801E13C01E', 'appium:newCommandTimeout': 300, 'appium:noReset': True, 'appium:automationName': 'XCUITest', 'appium:xcodeOrgId': '', 'appium:xcodeSigningId': 'iPhone Developer', 'appium:webDriverAgentUrl': 'http://localhost:8100', 'appium:showIOSLog': True}
[2025-07-08 19:04:00,679] INFO in appium_device_controller: Connecting to iOS device via WebDriverAgent
[2025-07-08 19:04:00,693] INFO in appium_device_controller: Device 00008120-00186C801E13C01E is listed and trusted
[2025-07-08 19:04:00,694] INFO in appium_device_controller: Found port 8100 for device 00008120-00186C801E13C01E in wda_ports.txt
[2025-07-08 19:04:00,694] INFO in appium_device_controller: Using WebDriverAgent URL: http://localhost:8100 for device 00008120-00186C801E13C01E
[2025-07-08 19:04:00,698] INFO in appium_device_controller: WebDriverAgent not detected at http://localhost:8100, will try to start port forwarding: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
[2025-07-08 19:04:00,699] INFO in appium_device_controller: Port 8100 is already in use, checking if WebDriverAgent is responding...
[2025-07-08 19:04:00,702] WARNING in appium_device_controller: Port 8100 is in use but WebDriverAgent is not responding: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer')). Will restart.
[2025-07-08 19:04:00,847] INFO in appium_device_controller: Killed process 3203 using port 8100
[2025-07-08 19:04:01,933] INFO in appium_device_controller: Using tidevice for port forwarding: 8100 -> 8100
[2025-07-08 19:04:02,237] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:04:02,238] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:04:02] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:04:02,240] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:04:02,241] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:04:02] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:04:03,943] INFO in appium_device_controller: tidevice port forwarding started successfully
[2025-07-08 19:04:03,943] INFO in appium_device_controller: Checking WebDriverAgent connection (attempt 1/5)...
[2025-07-08 19:04:03,948] WARNING in appium_device_controller: WebDriverAgent connection attempt 1 failed: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
[2025-07-08 19:04:06,950] INFO in appium_device_controller: Checking WebDriverAgent connection (attempt 2/5)...
[2025-07-08 19:04:06,963] INFO in appium_device_controller: WebDriverAgent is running at http://localhost:8100
[2025-07-08 19:04:06,963] INFO in appium_device_controller: WebDriverAgent status: {'value': {'build': {'version': '9.5.0', 'time': 'Jun  8 2025 18:35:21', 'productBundleIdentifier': 'com.facebook.WebDriverAgentRunner'}, 'os': {'testmanagerdVersion': 65535, 'name': 'iOS', 'sdkVersion': '18.4', 'version': '18.5'}, 'device': 'iphone', 'ios': {'ip': '************'}, 'message': 'WebDriverAgent is ready to accept commands', 'state': 'success', 'ready': True}, 'sessionId': None}
[2025-07-08 19:04:06,966] INFO in appium_device_controller: Appium server is already running
[2025-07-08 19:04:06,966] INFO in appium_device_controller: iOS connection attempt 1/3
[2025-07-08 19:04:06,966] INFO in appium_device_controller: Using capabilities: {'platformName': 'iOS', 'appium:deviceName': '00008120-00186C801E13C01E', 'appium:udid': '00008120-00186C801E13C01E', 'appium:newCommandTimeout': 300, 'appium:noReset': True, 'appium:automationName': 'XCUITest', 'appium:xcodeOrgId': '', 'appium:xcodeSigningId': 'iPhone Developer', 'appium:webDriverAgentUrl': 'http://localhost:8100', 'appium:showIOSLog': True, 'webDriverAgentUrl': 'http://localhost:8100'}
[2025-07-08 19:04:06,970] INFO in appium_device_controller: Appium server status before connection: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '97cfc553a41517b332064de3949071c6d64039d7', 'built': '2025-07-07 20:23:08 +1000'}}}
[2025-07-08 19:04:06,970] INFO in appium_device_controller: Connecting to Appium server at http://127.0.0.1:4723/wd/hub
[2025-07-08 19:04:07,235] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:04:07,236] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:04:07] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:04:07,239] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:04:07,239] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:04:07] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:04:08,213] INFO in appium_device_controller: Wrapping driver with Healenium self-healing capabilities
[2025-07-08 19:04:08,216] INFO in appium_device_controller: Driver successfully wrapped with Healenium
[2025-07-08 19:04:08,217] INFO in appium_device_controller: Successfully connected to iOS device
[2025-07-08 19:04:08,217] INFO in appium_device_controller: Connected with session ID: 605a7e74-0d5c-4fda-bdc5-a16fe12ccce2
[2025-07-08 19:04:08,217] INFO in appium_device_controller: Connection verified with capabilities: iOS
[2025-07-08 19:04:08,217] INFO in appium_device_controller: Initializing platform helpers for iOS
[2025-07-08 19:04:08,217] INFO in appium_device_controller: Getting device dimensions
[2025-07-08 19:04:08,556] INFO in appium_device_controller: Got device dimensions from Appium: 393x852
[2025-07-08 19:04:08,556] INFO in appium_device_controller: Device dimensions: (393, 852)
[2025-07-08 19:04:08,560] WARNING in appium_device_controller: Failed to initialize ImageMatcher: No module named 'app.utils'; 'app' is not a package
[2025-07-08 19:04:08,560] INFO in appium_device_controller: Initializing iOS-specific helpers
[2025-07-08 19:04:08,560] INFO in appium_device_controller: Setting up iOS predicate string and class chain support
[2025-07-08 19:04:08,560] INFO in appium_device_controller: iOS version: 18.0
[2025-07-08 19:04:08,560] INFO in appium_device_controller: Using modern keyboard handling for iOS 15+
[2025-07-08 19:04:08,560] INFO in appium_device_controller: Setting up Airtest helpers for iOS
[2025-07-08 19:04:08,560] INFO in appium_device_controller: Platform helpers initialization completed
[2025-07-08 19:04:08,560] INFO in appium_device_controller: Successfully connected to device on attempt 3
[2025-07-08 19:04:08,560] INFO in action_factory: Registered basic actions: tap, wait
[2025-07-08 19:04:08,562] INFO in action_factory: Special case: Registering tap_if_image_exists_action.py as 'tapIfImageExists'
[2025-07-08 19:04:08,562] INFO in action_factory: Registered action handler for 'tapIfImageExists'
[2025-07-08 19:04:08,562] INFO in action_factory: Registered action handler for 'multiStep'
[2025-07-08 19:04:08,563] INFO in action_factory: Special case: Registering cleanup_steps_action.py as 'cleanupSteps'
[2025-07-08 19:04:08,563] INFO in action_factory: Registered action handler for 'cleanupSteps'
[2025-07-08 19:04:08,564] INFO in action_factory: Registered action handler for 'swipe'
[2025-07-08 19:04:08,564] INFO in action_factory: Registered action handler for 'getParam'
[2025-07-08 19:04:08,565] INFO in action_factory: Registered action handler for 'wait'
[2025-07-08 19:04:08,565] INFO in action_factory: Registered action handler for 'terminateApp'
[2025-07-08 19:04:08,566] INFO in action_factory: Registered action handler for 'doubleClickImage'
[2025-07-08 19:04:08,567] INFO in action_factory: Registered action handler for 'uninstallApp'
[2025-07-08 19:04:08,567] INFO in action_factory: Registered action handler for 'text'
[2025-07-08 19:04:08,568] ERROR in action_factory: Failed to import module for tap_if_text_exists_action
[2025-07-08 19:04:08,570] INFO in action_factory: Registered action handler for 'waitTill'
[2025-07-08 19:04:08,570] INFO in action_factory: Registered action handler for 'hookAction'
[2025-07-08 19:04:08,571] INFO in action_factory: Registered action handler for 'inputText'
[2025-07-08 19:04:08,572] INFO in global_values_db: Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values.db
[2025-07-08 19:04:08,573] INFO in global_values_db: Global values database initialized successfully
[2025-07-08 19:04:08,573] INFO in global_values_db: Using global values from config.py
[2025-07-08 19:04:08,573] INFO in global_values_db: Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 2, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
[2025-07-08 19:04:08,574] INFO in action_factory: Registered action handler for 'setParam'
[2025-07-08 19:04:08,575] INFO in action_factory: Special case: Registering repeat_steps_action.py as 'repeatSteps'
[2025-07-08 19:04:08,575] INFO in action_factory: Registered action handler for 'repeatSteps'
[2025-07-08 19:04:08,576] INFO in action_factory: Registered action handler for 'iosFunctions'
[2025-07-08 19:04:08,577] INFO in action_factory: Registered action handler for 'swipeTillVisible'
[2025-07-08 19:04:08,577] INFO in action_factory: Registered action handler for 'clickImage'
[2025-07-08 19:04:08,578] INFO in action_factory: Registered action handler for 'tap'
[2025-07-08 19:04:08,579] INFO in action_factory: Registered action handler for 'ifElseSteps'
[2025-07-08 19:04:08,579] INFO in action_factory: Special case: Registering take_screenshot_action.py as 'takeScreenshot'
[2025-07-08 19:04:08,579] INFO in action_factory: Registered action handler for 'takeScreenshot'
[2025-07-08 19:04:08,580] INFO in action_factory: Special case: Registering tap_if_locator_exists_action.py as 'tapIfLocatorExists'
[2025-07-08 19:04:08,580] INFO in action_factory: Registered action handler for 'tapIfLocatorExists'
[2025-07-08 19:04:08,581] INFO in action_factory: Registered action handler for 'tapAndType'
[2025-07-08 19:04:08,582] INFO in action_factory: Special case: Registering tap_on_text_action.py as 'tapOnText'
[2025-07-08 19:04:08,582] INFO in action_factory: Registered action handler for 'tapOnText'
[2025-07-08 19:04:08,582] INFO in action_factory: Registered action handler for 'launchApp'
[2025-07-08 19:04:08,583] INFO in action_factory: Special case: Registering info_action.py as 'info'
[2025-07-08 19:04:08,583] INFO in action_factory: Registered action handler for 'info'
[2025-07-08 19:04:08,583] INFO in action_factory: Registered action handler for 'waitElement'
[2025-07-08 19:04:08,584] INFO in action_factory: Registered action handler for 'compareValue'
[2025-07-08 19:04:08,584] INFO in action_factory: Registered action handler for 'deviceBack'
[2025-07-08 19:04:08,585] INFO in action_factory: Special case: Registering check_if_exists_action.py as 'exists'
[2025-07-08 19:04:08,585] INFO in action_factory: Registered action handler for 'exists'
[2025-07-08 19:04:08,586] INFO in action_factory: Registered action handler for 'clickElement'
[2025-07-08 19:04:08,586] INFO in action_factory: Registered action handler for 'randomData'
[2025-07-08 19:04:08,587] INFO in action_factory: Registered action handler for 'getValue'
[2025-07-08 19:04:08,587] INFO in action_factory: Registered action handler for 'test'
[2025-07-08 19:04:08,588] INFO in action_factory: Registered action handler for 'restartApp'
[2025-07-08 19:04:08,588] INFO in action_factory: Special case: Registering double_tap_action.py as 'doubleTap'
[2025-07-08 19:04:08,588] INFO in action_factory: Registered action handler for 'doubleTap'
[2025-07-08 19:04:08,589] INFO in action_factory: Registered action types: ['tap', 'wait', 'tapIfImageExists', 'multiStep', 'cleanupSteps', 'swipe', 'getParam', 'terminateApp', 'doubleClickImage', 'uninstallApp', 'text', 'waitTill', 'hookAction', 'inputText', 'setParam', 'repeatSteps', 'iosFunctions', 'swipeTillVisible', 'clickImage', 'ifElseSteps', 'takeScreenshot', 'tapIfLocatorExists', 'tapAndType', 'tapOnText', 'launchApp', 'info', 'waitElement', 'compareValue', 'deviceBack', 'exists', 'clickElement', 'randomData', 'getValue', 'test', 'restartApp', 'doubleTap']
[2025-07-08 19:04:08,589] INFO in action_factory: Handler for 'tap': TapAction
[2025-07-08 19:04:08,589] INFO in action_factory: Handler for 'wait': WaitAction
[2025-07-08 19:04:08,589] INFO in action_factory: Handler for 'tapIfImageExists': TapIfImageExistsAction
[2025-07-08 19:04:08,589] INFO in action_factory: Handler for 'multiStep': MultiStepAction
[2025-07-08 19:04:08,589] INFO in action_factory: Handler for 'cleanupSteps': CleanupStepsAction
[2025-07-08 19:04:08,589] INFO in action_factory: Handler for 'swipe': SwipeAction
[2025-07-08 19:04:08,589] INFO in action_factory: Handler for 'getParam': GetParamAction
[2025-07-08 19:04:08,589] INFO in action_factory: Handler for 'terminateApp': TerminateAppAction
[2025-07-08 19:04:08,589] INFO in action_factory: Handler for 'doubleClickImage': DoubleClickImageAction
[2025-07-08 19:04:08,589] INFO in action_factory: Handler for 'uninstallApp': UninstallAppAction
[2025-07-08 19:04:08,589] INFO in action_factory: Handler for 'text': TextAction
[2025-07-08 19:04:08,589] INFO in action_factory: Handler for 'waitTill': WaitTillAction
[2025-07-08 19:04:08,589] INFO in action_factory: Handler for 'hookAction': HookAction
[2025-07-08 19:04:08,589] INFO in action_factory: Handler for 'inputText': InputTextAction
[2025-07-08 19:04:08,589] INFO in action_factory: Handler for 'setParam': SetParamAction
[2025-07-08 19:04:08,589] INFO in action_factory: Handler for 'repeatSteps': RepeatStepsAction
[2025-07-08 19:04:08,589] INFO in action_factory: Handler for 'iosFunctions': IosFunctionsAction
[2025-07-08 19:04:08,589] INFO in action_factory: Handler for 'swipeTillVisible': SwipeTillVisibleAction
[2025-07-08 19:04:08,589] INFO in action_factory: Handler for 'clickImage': ClickImageAction
[2025-07-08 19:04:08,589] INFO in action_factory: Handler for 'ifElseSteps': IfElseStepsAction
[2025-07-08 19:04:08,589] INFO in action_factory: Handler for 'takeScreenshot': TakeScreenshotAction
[2025-07-08 19:04:08,589] INFO in action_factory: Handler for 'tapIfLocatorExists': TapIfLocatorExistsAction
[2025-07-08 19:04:08,589] INFO in action_factory: Handler for 'tapAndType': TapAndTypeAction
[2025-07-08 19:04:08,589] INFO in action_factory: Handler for 'tapOnText': TapOnTextAction
[2025-07-08 19:04:08,589] INFO in action_factory: Handler for 'launchApp': LaunchAppAction
[2025-07-08 19:04:08,589] INFO in action_factory: Handler for 'info': InfoAction
[2025-07-08 19:04:08,589] INFO in action_factory: Handler for 'waitElement': WaitElementAction
[2025-07-08 19:04:08,589] INFO in action_factory: Handler for 'compareValue': CompareValueAction
[2025-07-08 19:04:08,589] INFO in action_factory: Handler for 'deviceBack': DeviceBackAction
[2025-07-08 19:04:08,590] INFO in action_factory: Handler for 'exists': CheckIfExistsAction
[2025-07-08 19:04:08,590] INFO in action_factory: Handler for 'clickElement': ClickElementAction
[2025-07-08 19:04:08,590] INFO in action_factory: Handler for 'randomData': RandomDataAction
[2025-07-08 19:04:08,590] INFO in action_factory: Handler for 'getValue': GetValueAction
[2025-07-08 19:04:08,590] INFO in action_factory: Handler for 'test': TestAction
[2025-07-08 19:04:08,590] INFO in action_factory: Handler for 'restartApp': RestartAppAction
[2025-07-08 19:04:08,590] INFO in action_factory: Handler for 'doubleTap': DoubleTapAction
[2025-07-08 19:04:08,590] INFO in appium_device_controller: Initializing Airtest connection for device: 00008120-00186C801E13C01E...
[2025-07-08 19:04:08,590] INFO in appium_device_controller: Connecting to iOS device with WebDriverAgent at http://localhost:8100
[2025-07-08 19:04:08,595] INFO in ios_device: Initialized MinimalIOSDevice for 00008120-00186C801E13C01E with WDA at http://localhost:8100
[2025-07-08 19:04:08,599] ERROR in appium_device_controller: Failed to get screen resolution from iOS device
[2025-07-08 19:04:08,599] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-08 19:04:08,600] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp/screenshots/placeholder.png (save_debug=False)
[2025-07-08 19:04:08,600] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-08 19:04:09,776] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-08 19:04:09,777] INFO in appium_device_controller: suite_id: None
[2025-07-08 19:04:09,777] INFO in appium_device_controller: test_idx: 0
[2025-07-08 19:04:09,777] INFO in appium_device_controller: step_idx: 0
[2025-07-08 19:04:09,777] INFO in appium_device_controller: filename: placeholder.png
[2025-07-08 19:04:09,777] INFO in appium_device_controller: action_id: placeholder
[2025-07-08 19:04:09,778] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-08 19:04:09,778] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-08 19:04:10,094] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:04:10] "POST /api/device/connect HTTP/1.1" 200 -
[2025-07-08 19:04:11,106] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_00008120_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-08 19:04:11,106] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-08 19:04:12,231] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-08 19:04:12,231] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_00008120_00186C801E13C01E_latest.png
[2025-07-08 19:04:12,231] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:04:12] "GET /screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1751965402235_e8u44n2gk_1751965402235_e8u44n2gk&t=1751965451104 HTTP/1.1" 200 -
[2025-07-08 19:04:12,236] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:04:12,237] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:04:12] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:04:12,239] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:04:12,240] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:04:12] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:04:17,235] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:04:17,235] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:04:17] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:04:17,237] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:04:17,238] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:04:17] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:04:22,237] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:04:22,239] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:04:22] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:04:22,241] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:04:22,242] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:04:22] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:04:27,235] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:04:27,236] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:04:27] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:04:29,441] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_00008120_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-08 19:04:29,441] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-08 19:04:30,586] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-08 19:04:30,586] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_00008120_00186C801E13C01E_latest.png
[2025-07-08 19:04:30,587] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:04:30] "GET /screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1751965402235_e8u44n2gk_1751965402235_e8u44n2gk&t=1751965469439 HTTP/1.1" 200 -
[2025-07-08 19:04:32,237] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:04:32,238] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:04:32] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:04:37,238] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:04:37,240] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:04:37] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:04:40,108] DEBUG in appium_device_controller: Session ID: 605a7e74-0d5c-4fda-bdc5-a16fe12ccce2
[2025-07-08 19:04:40,426] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-08 19:04:40,427] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:04:40] "GET /api/session/health HTTP/1.1" 200 -
[2025-07-08 19:04:42,237] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:04:42,238] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:04:42] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:04:47,236] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:04:47,237] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:04:47] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:04:52,238] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:04:52,239] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:04:52] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:04:57,241] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:04:57,242] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:04:57] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:05:02,238] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:05:02,239] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:05:02] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:05:07,235] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:05:07,236] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:05:07] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:05:10,109] DEBUG in appium_device_controller: Session ID: 605a7e74-0d5c-4fda-bdc5-a16fe12ccce2
[2025-07-08 19:05:10,419] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-08 19:05:10,419] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:05:10] "GET /api/session/health HTTP/1.1" 200 -
[2025-07-08 19:05:12,237] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:05:12,239] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:05:12] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:05:17,237] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:05:17,239] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:05:17] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:05:22,236] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:05:22,236] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:05:22] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:05:27,236] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:05:27,237] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:05:27] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:05:32,236] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:05:32,236] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:05:32] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:05:37,236] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:05:37,237] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:05:37] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:05:40,106] DEBUG in appium_device_controller: Session ID: 605a7e74-0d5c-4fda-bdc5-a16fe12ccce2
[2025-07-08 19:05:40,412] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-08 19:05:40,412] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:05:40] "GET /api/session/health HTTP/1.1" 200 -
[2025-07-08 19:05:42,236] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:05:42,237] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:05:42] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:05:47,235] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:05:47,236] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:05:47] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:05:52,236] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:05:52,237] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:05:52] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:05:57,235] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:05:57,236] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:05:57] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:06:02,236] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:06:02,236] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:06:02] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:06:07,236] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:06:07,237] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:06:07] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:06:10,107] DEBUG in appium_device_controller: Session ID: 605a7e74-0d5c-4fda-bdc5-a16fe12ccce2
[2025-07-08 19:06:10,416] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-08 19:06:10,417] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:06:10] "GET /api/session/health HTTP/1.1" 200 -
[2025-07-08 19:06:12,236] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:06:12,236] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:06:12] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:06:17,237] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:06:17,238] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:06:17] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:06:22,236] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:06:22,237] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:06:22] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:06:27,235] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:06:27,236] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:06:27] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:06:36,797] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:06:36,798] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:06:36] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:06:38,734] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:06:38,735] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:06:38] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:06:40,107] DEBUG in appium_device_controller: Session ID: 605a7e74-0d5c-4fda-bdc5-a16fe12ccce2
[2025-07-08 19:06:40,516] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-08 19:06:40,516] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:06:40] "GET /api/session/health HTTP/1.1" 200 -
[2025-07-08 19:06:42,235] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:06:42,236] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:06:42] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:06:47,235] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:06:47,236] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:06:47] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:06:54,679] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:06:54,680] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:06:54] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:06:57,235] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:06:57,236] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:06:57] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:07:02,805] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:07:02,807] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:07:02] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:07:09,295] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:07:09,297] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:07:09] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:07:14,749] DEBUG in appium_device_controller: Session ID: 605a7e74-0d5c-4fda-bdc5-a16fe12ccce2
[2025-07-08 19:07:14,752] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:07:14,754] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:07:14] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:07:15,162] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-08 19:07:15,162] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:07:15] "GET /api/session/health HTTP/1.1" 200 -
[2025-07-08 19:07:21,136] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:07:21,136] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:07:21] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:07:25,097] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:07:25,098] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:07:25] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:07:27,558] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: None ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: None ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: None ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: None ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: None ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: None ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: None ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: None ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: None ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: None ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: None ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: None ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: None ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: None ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: None ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: None ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: None ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: None ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: None ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: None ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: None ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: None ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: None ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: None ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: None ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: None ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: None ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: None ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: None ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: None ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===[2025-07-08 19:07:27,559] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:07:27] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:07:33,948] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:07:33,949] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:07:33] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:07:44,055] DEBUG in appium_device_controller: Session ID: 605a7e74-0d5c-4fda-bdc5-a16fe12ccce2
[2025-07-08 19:07:44,056] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:07:44,057] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:07:44] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:07:44,453] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-08 19:07:44,453] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:07:44] "GET /api/session/health HTTP/1.1" 200 -
[2025-07-08 19:07:54,559] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:07:54,560] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:07:54] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:08:04,674] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:08:04,675] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:08:04] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:08:47,249] DEBUG in appium_device_controller: Session ID: 605a7e74-0d5c-4fda-bdc5-a16fe12ccce2
[2025-07-08 19:08:47,249] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-08 19:08:47,250] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:08:47] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-08 19:08:47,566] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-08 19:08:47,566] INFO in _internal: 127.0.0.1 - - [08/Jul/2025 19:08:47] "GET /api/session/health HTTP/1.1" 200 -
