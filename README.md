# Mobile App Automation Tool

A Python-based mobile app automation tool with record and playback capabilities, built using Flask, OpenCV, Appium, and ADB. Features element detection and text recognition for both Android and iOS.

## Features

- Connect to **Android** devices via ADB
- **NEW:** Connect to **iOS** devices via Appium XCUITest driver
- **NEW:** **Docker Support:** Containerized deployment for consistent environments
- **NEW:** **BrowserStack Integration:** Cloud device testing with encrypted credential management
- **Multi-Device Parallel Testing:** Run 2-20+ sessions simultaneously with different devices
- Record user interactions (taps, swipes, text input) across platforms
- Playback recorded actions
- Visual validation using OpenCV (if needed)
- Generate Python code from recorded actions
- Web-based user interface with session isolation
- Element detection and text recognition (platform-aware)
- Optimized performance for long recordings
- Element-based targeting (find and interact with UI elements by text/type/ID/etc.)
- Integrated Appium server with Inspector-like element identification
- Scalable architecture supporting enterprise-level parallel testing
- **Cloud Testing:** Support for both physical devices and cloud-based testing platforms

## Requirements

### General

- Python 3.8+ (Python 3.12 supported with specific package versions)
- Node.js and npm (for Appium server and its drivers)
- Tesseract <PERSON>CR (optional, for OCR-based text detection)
    - macOS: `brew install tesseract`
    - Linux: `sudo apt install tesseract-ocr`
    - Windows: Install from [UB-Mannheim/tesseract](https://github.com/UB-Mannheim/tesseract/wiki)

### Android Testing

- ADB tools installed and in your PATH
    - macOS: `brew install android-platform-tools`
    - Linux: `sudo apt install adb`
    - Windows: Download [Android SDK Platform Tools](https://developer.android.com/studio/releases/platform-tools) and add to PATH
- An Android device or emulator with USB debugging enabled

### iOS Testing (Requires macOS Host)

Testing on **physical iOS devices or simulators requires a macOS machine** due to Apple's constraints.

- **Xcode:** The correct version for your macOS must be installed.
    - **Install:** Download a compatible version from the [Apple Developer Downloads page](https://developer.apple.com/download/all/). You **cannot** use a version that requires a newer macOS than you have installed. Search for Xcode, find the latest version supported by your macOS, download the `.xip`, extract it, and move `Xcode.app` to `/Applications`.
    - **Launch:** Launch Xcode at least once to accept licenses and install components.
- **Xcode Command Line Tools:** Required for Appium to interact with Xcode.
    - **Set Path:** Ensure `xcode-select` points to the tools *inside* your installed Xcode. Run in Terminal:
      ```bash
      sudo xcode-select -s /Applications/Xcode.app/Contents/Developer
      ```
      Verify with `xcode-select -p`.
    - **Install/Update:** Run `xcode-select --install` and follow prompts.
- **Homebrew:** The standard macOS package manager is needed to install dependencies.
    - Install from [https://brew.sh/](https://brew.sh/) if you don't have it.
- **Core Dependencies:** Install via Homebrew:
    ```bash
    brew install libimobiledevice carthage ios-deploy
    ```
    - `libimobiledevice`: For basic communication with iOS devices (finding UDID, etc.).
    - `carthage`: Dependency manager needed by Appium/WebDriverAgent.
    - `ios-deploy`: Helps Appium install apps onto real devices.
- **Python Dependency for Airtest/iOS:**
    ```bash
    pip install -U tidevice
    ```
    - `tidevice`: Used for certain interactions, particularly if using Airtest features on iOS.
- **Appium XCUITest Driver:** Appium manages this, but ensure it's available:
    ```bash
    appium driver list | cat # Check if xcuitest is listed
    # If not listed, install:
    appium driver install xcuitest
    ```
- **Apple Developer Account:** Required for running apps (like WebDriverAgent) on physical devices.
You will need your **Team ID** (find on [developer.apple.com](https://developer.apple.com/)) and the correct **Bundle ID** for the app you intend to test. These need to be configured within the application code (see `app/utils/appium_device_controller.py`).

## Installation

### Automated Setup (Recommended)

1. Clone this repository:
```bash
git clone <your-repo-url> # Replace with the actual URL
cd <your-repo-directory>
```

2. Run the setup script:
   - On macOS/Linux:
   ```bash
   chmod +x setup.sh
   ./setup.sh
   ```
   - On Windows:
   ```bash
   setup.bat
   ```

The setup script will:
- Create a Python virtual environment
- Install all Python dependencies
- Install Node.js dependencies including Appium and its drivers
- Check for platform-specific dependencies
- Provide guidance for any missing components

### Manual Setup

If you prefer to set up manually:

1. Clone this repository and navigate to the directory

2. Create a Python virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install Python dependencies:
```bash
pip install -r requirements.txt
```
   **Note:** The requirements now include additional dependencies for Docker support (`docker>=6.1.0`, `psycopg2-binary>=2.9.0`) and BrowserStack integration (`cryptography>=41.0.0`).

4. Install Node.js dependencies (includes Appium locally):
```bash
npm install
```

5. Install Appium drivers locally:
```bash
npx appium driver install uiautomator2
npx appium driver install xcuitest
```

6. **Configure iOS Capabilities (if testing iOS):**
   - Edit the file `app/utils/appium_device_controller.py`.
   - Locate the `# --- iOS Specific Capabilities ---` section within the `connect_to_device` method.
   - Replace the placeholder values for `bundleId`, `xcodeOrgId`, and `xcodeSigningId` with your actual app's Bundle ID, your Apple Developer Team ID, and the appropriate signing identity (usually 'iPhone Developer').

### Docker Setup (Recommended for Production)

For containerized deployment with consistent environments:

1. **Prerequisites:**
   - Docker and Docker Compose installed
   - For detailed Docker setup instructions, see [DOCKER_DEPLOYMENT_GUIDE.md](DOCKER_DEPLOYMENT_GUIDE.md)

2. **Quick Start:**
```bash
# Build and start the container
docker-compose up --build

# Or use the deployment script
./deploy-docker.sh
```

3. **Access the application:**
   - iOS Platform: http://localhost:8080
   - Android Platform: http://localhost:8081

### BrowserStack Cloud Testing Setup

For cloud device testing with BrowserStack:

1. **Prerequisites:**
   - BrowserStack account with App Automate subscription
   - App uploaded to BrowserStack (get the `bs://` app ID)

2. **Configuration:**
   - Navigate to the "Cloud Device" tab in the web interface
   - Enter your BrowserStack username and access key
   - Provide the BrowserStack app ID (format: `bs://abc123...`)
   - Credentials are encrypted and stored locally

3. **Features:**
   - Secure credential management with encryption
   - Support for both iOS and Android cloud devices
   - Seamless integration with existing test cases
   - Real-time device interaction through BrowserStack

## Usage

### Starting the App

The app now automatically manages the Appium server and other processes. Simply run:

```bash
cd /path/to/MobileApp-AutoTest
source venv/bin/activate  # On Windows: venv\Scripts\activate
python run.py
```

This will:
1. Kill any existing Appium and iproxy processes
2. Start a new Appium server with the correct configuration
3. Launch the web application

When you close the app (Ctrl+C), it will automatically clean up all processes.

#### Access the app

Open your browser and navigate to `http://localhost:8080`

### Connecting Devices

1. Connect your device:
   - **Android:** Enable USB debugging, connect via USB, approve connection, select device, click "Connect".
   - **iOS (on Mac):** Ensure device is connected via USB and **trusted** (tap "Trust This Computer" on the iPhone), select device (should show UDID and name), click "Connect".

2. Recording/Playback/Inspection:
   - Use the web interface to record and play back actions
   - Actions like tap, swipe, etc., are platform-aware
   - Use the Inspector tab for element identification

### Multi-Device Parallel Testing

The application supports running multiple sessions simultaneously for parallel testing across different devices. This is ideal for:
- Testing the same app on multiple device models
- Running different test suites in parallel
- Cross-platform testing (iOS and Android simultaneously)
- Load testing with multiple user scenarios

#### Quick Start - Multiple Sessions

**Method 1: Automated Script (Recommended)**
```bash
# Start 3 parallel sessions
./start_multi_device.sh --instances 3

# Start 5 sessions with custom ports
./start_multi_device.sh --instances 5 --base-port 9000 --base-appium 5000
```

**Method 2: Manual Sessions**
```bash
# Terminal 1 - Default session
python run.py

# Terminal 2 - Second session
python run.py --port 8081 --appium-port 4724

# Terminal 3 - Third session
python run.py --port 8082 --appium-port 4725
```

#### System Capacity

| Platform | Recommended Max | System Requirements |
|----------|----------------|-------------------|
| **iOS Devices** | 5-8 sessions | 16GB RAM, USB 3.0+ hubs |
| **Android Devices** | 8-15 sessions | 16GB RAM, stable ADB |
| **Mixed Platform** | 6-10 sessions | 16GB+ RAM, 8+ core CPU |

#### Important Notes

- **Don't use `--wda-port`** for multi-device setups - WebDriverAgent ports are auto-assigned per device
- Each session uses ~200-500MB RAM and 5-15% CPU
- Ensure adequate USB bandwidth for multiple iOS devices
- See `MULTI_SESSION_SETUP.md` for comprehensive setup guide

#### Access Multiple Sessions

Once started, access each session in separate browser tabs:
- Session 1: http://localhost:8080
- Session 2: http://localhost:8081
- Session 3: http://localhost:8082
- etc.

Each session operates independently with its own device connection, test data, and reports.

## Troubleshooting

### Installation Issues

- **Setup Script Fails:** If the automated setup script fails, try the manual setup steps one by one to identify the issue.
- **Python Virtual Environment:** If you see errors about missing packages, ensure your virtual environment is activated (`source venv/bin/activate` or `venv\Scripts\activate` on Windows).
- **Node.js Permissions:** If npm install fails with permission errors, try running with sudo (on Linux/macOS) or as administrator (on Windows).
- **OpenCV:** If you encounter OpenCV-related errors, try switching between versions:
  ```bash
  pip uninstall opencv-python-headless opencv-python
  pip install opencv-python
  ```
- **Appium Drivers:** If driver installation fails, try installing them manually:
  ```bash
  # First, ensure you're in the project directory
  cd /path/to/MobileApp-AutoTest
  # Then run
  ./node_modules/.bin/appium driver install xcuitest
  ./node_modules/.bin/appium driver install uiautomator2
  ```

### Connection Issues

- **Appium Server Not Starting:** Check if the port is already in use:
  ```bash
  lsof -i :4723  # On macOS/Linux
  netstat -ano | findstr :4723  # On Windows
  ```
  If it's in use, kill the process and try again.

- **Android Device Not Detected:**
  - Ensure USB debugging is enabled on the device
  - Check connection with `adb devices`
  - Try restarting ADB: `adb kill-server && adb start-server`
  - Try a different USB cable or port

- **iOS Device Not Detected:**
  - Ensure the device is unlocked
  - Make sure you've tapped "Trust This Computer" on the device
  - Verify Xcode and WebDriverAgent setup:
    ```bash
    xcode-select -p  # Should point to /Applications/Xcode.app/Contents/Developer
    brew list libimobiledevice  # Should be installed
    pip show tidevice  # Should be installed
    ```
  - Check WebDriverAgent logs in the Appium server output
  - For persistent issues, try manually building WebDriverAgent in Xcode first

### App Startup Issues

- **App Fails to Start:** Check if all dependencies are installed correctly:
  ```bash
  source venv/bin/activate
  pip list  # Verify Python packages
  npm list --depth=0  # Verify Node.js packages
  ```

- **Port Already in Use:** If you see "Address already in use" errors, find and kill the process using that port:
  ```bash
  # For port 8080 (app server)
  lsof -i :8080  # On macOS/Linux
  netstat -ano | findstr :8080  # On Windows

  # For port 4723 (Appium server)
  lsof -i :4723  # On macOS/Linux
  netstat -ano | findstr :4723  # On Windows
  ```
  Then kill the process and try again.

## Recent Updates

- **Added iOS Support:** Connect to and automate iOS devices via Appium/XCUITest.
- **Platform-Aware Actions:** Core functions like connect, screenshot, tap, find elements now handle Android/iOS differences.
- **Refactored Setup:** Clearer separation of requirements for Android vs. iOS.
- Removed automatic screenshot polling to improve performance.
- Optimized screenshot capturing (only when device connects or actions execute).
- Improved screenshot management (single file instead of multiple files).
- Consolidated static directories to avoid path confusion.
- Removed unused files and redundant code.
- Simplified installation process.
- Optimized dependencies.
- Improved error handling and logging.
- **Added Comprehensive Documentation:** Generated detailed documentation covering architecture, API, and usage guides.

## Documentation

This project includes comprehensive documentation to help you understand, use, and extend the MobileApp-AutoTest tool.

### Generating Documentation

To generate or update the documentation, run:

```bash
./generate_docs.sh
```

This will:
1. Analyze the codebase structure
2. Extract API endpoints, functions, and classes
3. Generate documentation files in Markdown format
4. Convert the Markdown to HTML for browsing

### Documentation Structure

The documentation is organized into the following sections:

- **Project Overview:** High-level understanding of the project's purpose and features
- **Architecture:** System components and their relationships
- **API Reference:** Details of all available API endpoints
- **Modules:** Documentation for each module in the codebase
- **Guides:** Step-by-step instructions for common tasks
- **Troubleshooting:** Solutions to common issues

### Viewing Documentation

You can view the documentation in two formats:

- **Markdown:** Browse the files in the `docs/` directory
- **HTML:** Open `docs/html/index.html` in your web browser

The HTML documentation provides a more user-friendly experience with navigation and styling.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

# MobileApp-AutoTest Testing Framework

This documentation explains the testing framework for the MobileApp-AutoTest application. The framework helps ensure code quality and prevents regressions when making changes to the codebase.

## Image Comparison

The application includes powerful image comparison tools that allow you to compare screenshots between test runs and identify visual differences. This is useful for visual regression testing to ensure UI changes don't break existing functionality.

### Using Image Comparison

The image comparison tools are located in the `image_comparison` directory and provide several ways to compare images:

#### Generating HTML Comparison Reports

To generate a visual comparison report between baseline and new images:

```bash
# Activate the virtual environment
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Generate a comparison report
python image_comparison/generate_html_report.py --baseline <baseline_dir> --new <new_dir> --output <output_dir> --threshold <similarity_threshold>
```

Parameters:
- `--baseline`: Directory containing your baseline/reference images
- `--new`: Directory containing your new/test images to compare against the baseline
- `--output`: Directory where the HTML report will be saved (will be created if it doesn't exist)
- `--threshold`: Similarity threshold (0.0-1.0) for considering images as matching (default: 0.95)

Example:
```bash
python image_comparison/generate_html_report.py --baseline image_comparison/baseline --new image_comparison/new --output image_comparison/report --threshold 0.95
```

#### Comparing Individual Images

To compare two specific images:

```bash
python image_comparison/compare_images.py <image1_path> <image2_path>
```

Example:
```bash
python image_comparison/compare_images.py image_comparison/1.png image_comparison/2.png
```

This will:
- Compare the two images
- Calculate a similarity score
- Generate a visual diff image showing the differences
- Return a JSON result with similarity metrics

#### Creating Sample Images for Testing

If you need sample images to test the comparison functionality:

```bash
python image_comparison/create_sample_images.py
```

This creates sample images in the `baseline` and `new` directories with action IDs that match the format used in the system.

### Visual Regression Testing Workflow

For a complete visual regression testing workflow:

1. **Create a baseline**: Run your tests and save screenshots as baseline images
2. **Make changes**: Make changes to your application
3. **Run tests again**: Run the same tests to generate new screenshots
4. **Compare results**: Generate a comparison report
   ```bash
   python image_comparison/generate_html_report.py --baseline reports/baseline/screenshots --new reports/new_run/screenshots --output comparison_report
   ```
5. **Review differences**: Open the generated HTML report to see visual differences

### Report Contents

The generated HTML report includes:
- A summary of all image comparisons
- Pass/fail status for each comparison based on the threshold
- Side-by-side visual comparison of baseline and new images
- Similarity percentage for each comparison
- Sorting of results with failures shown first

## Generating Reports with Proper Screenshots

The `regenerate_report.py` script allows you to generate HTML reports with proper screenshots for test suites or individual test cases. This is useful when screenshots aren't properly linked to test steps in the original report.

### Usage

```bash
# Activate the virtual environment
source venv/bin/activate  # On Windows: venv\Scripts\activate

# For a test suite (processes all test cases in the suite at once):
python regenerate_report.py suites/your_test_suite_file.json reports/testsuite_execution_YYYYMMDD_HHMMSS

# For a single test case:
python regenerate_report.py test_cases/your_test_case_file.json reports/testsuite_execution_YYYYMMDD_HHMMSS
```

### Example

```bash
# Process an entire test suite
python regenerate_report.py suites/36840143-dcab-4075-8ade-ca5522e01e1d.json reports/testsuite_execution_20250511_163417

# Process a single test case
python regenerate_report.py test_cases/Kmart_Prod__Onboarding2_20250422143908.json reports/testsuite_execution_20250511_163417
```

### How It Works

1. The script loads the test suite or test case file to get the correct action_ids
2. It updates the data.json file in the report folder with these action_ids
3. It regenerates the HTML report with the correct screenshot mappings
4. The report will now show the proper screenshots for each test step

### Troubleshooting

If you encounter issues:

1. Make sure the test suite or test case file exists and contains valid action_ids
2. Verify that the report folder exists and contains a data.json file
3. Check that screenshots exist in the report's screenshots directory with the correct action_id-based filenames
4. Run the script with the virtual environment activated

## Test Structure

The test suite is organized into the following structure:

```
tests/
├── unit/                  # Unit tests for individual modules
│   ├── main.test.js       # Tests for current monolithic main.js
│   └── modules/           # Tests for future modularized code
│       ├── device.test.js # Tests for device-related functionality
│       └── actions.test.js # Tests for action-related functionality
├── integration/           # Tests for module interactions
│   └── app.test.js        # Application integration tests
├── mocks/                 # Mock data for tests
│   └── appium-responses.js # Mock Appium API responses
└── jest.setup.js          # Jest configuration and global mocks
```

## Running Tests

To run tests, use the following npm commands:

```bash
# Run all tests
npm test

# Run tests in watch mode (automatically re-run when files change)
npm run test:watch

# Run tests with coverage report
npm run test:coverage

# Run specific test files
npm run test:main     # Tests for main.js
npm run test:device   # Tests for device module
npm run test:actions  # Tests for actions module
npm run test:integration  # Run all integration tests

# Run tests matching a specific name
npm test -- -t "should refresh screenshot"

# Run a specific test file
npm test -- tests/unit/main.test.js
```

## Writing Tests

When adding new features or fixing bugs:

1. Write a test that reproduces the issue or tests the new feature
2. Make sure all existing tests pass with your changes
3. Check the code coverage to ensure your changes are adequately tested

## Test Guidelines

- **Unit tests** should focus on testing a single function or method in isolation
- **Integration tests** should test how different parts of the application work together
- Use `jest.mock()` to mock external dependencies
- Use the mock data in `tests/mocks/` for consistent test fixtures

## Future Refactoring

The test structure is designed to support future refactoring of the main.js file into modules:

- `device.js` - Device connection and screenshot management
- `actions.js` - Action execution and management
- `ui.js` - UI updates and event handling
- `testcase.js` - Test case management

When refactoring, implement each module with proper interfaces that match the test expectations.

## Troubleshooting Tests

If tests are failing:

1. Check if you've broken existing functionality
2. Ensure mocks are correctly set up for your tests
3. Use `jest.spyOn()` to verify that specific functions are called
4. Run specific tests with `npm test -- -t 'test name'` to isolate issues

## Best Practices

- Keep tests fast and focused
- Avoid testing implementation details; test behavior instead
- Make tests deterministic (no random values)
- Clean up after each test to prevent state leakage