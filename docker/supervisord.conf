[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid

[program:appium-ios]
command=npx appium --port 4723 --use-plugins=inspector --allow-cors --relaxed-security
directory=/app
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/appium-ios.err.log
stdout_logfile=/var/log/supervisor/appium-ios.out.log
environment=PATH="/opt/venv/bin:/usr/local/bin:/usr/bin:/bin",JAVA_HOME="/usr/lib/jvm/java-11-openjdk-amd64",ANDROID_HOME="/opt/android-sdk",ANDROID_SDK_ROOT="/opt/android-sdk"

[program:appium-android]
command=npx appium --port 4724 --use-plugins=inspector --allow-cors --relaxed-security
directory=/app
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/appium-android.err.log
stdout_logfile=/var/log/supervisor/appium-android.out.log
environment=PATH="/opt/venv/bin:/usr/local/bin:/usr/bin:/bin",JAVA_HOME="/usr/lib/jvm/java-11-openjdk-amd64",ANDROID_HOME="/opt/android-sdk",ANDROID_SDK_ROOT="/opt/android-sdk"

[program:ios-app]
command=/opt/venv/bin/python run.py --port 8080 --appium-port 4723 --wda-port 8101
directory=/app
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/ios-app.err.log
stdout_logfile=/var/log/supervisor/ios-app.out.log
environment=PATH="/opt/venv/bin:/usr/local/bin:/usr/bin:/bin",JAVA_HOME="/usr/lib/jvm/java-11-openjdk-amd64",ANDROID_HOME="/opt/android-sdk",ANDROID_SDK_ROOT="/opt/android-sdk"

[program:android-app]
command=/opt/venv/bin/python run_android.py --port 8081 --appium-port 4724 --wda-port 8102
directory=/app
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/android-app.err.log
stdout_logfile=/var/log/supervisor/android-app.out.log
environment=PATH="/opt/venv/bin:/usr/local/bin:/usr/bin:/bin",JAVA_HOME="/usr/lib/jvm/java-11-openjdk-amd64",ANDROID_HOME="/opt/android-sdk",ANDROID_SDK_ROOT="/opt/android-sdk"