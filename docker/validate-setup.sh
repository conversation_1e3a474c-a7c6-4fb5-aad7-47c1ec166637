#!/bin/bash

# Validation script for Mobile App Automation Tool Docker setup

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

echo -e "${BLUE}=== Mobile App Automation Tool - Docker Setup Validation ===${NC}"
echo

# Check Docker installation
print_info "Checking Docker installation..."
if command -v docker &> /dev/null; then
    docker_version=$(docker --version)
    print_status "Docker is installed: $docker_version"
else
    print_error "Docker is not installed"
    exit 1
fi

# Check Docker Compose installation
print_info "Checking Docker Compose installation..."
if command -v docker-compose &> /dev/null; then
    compose_version=$(docker-compose --version)
    print_status "Docker Compose is installed: $compose_version"
else
    print_error "Docker Compose is not installed"
    exit 1
fi

# Check if Docker daemon is running
print_info "Checking Docker daemon..."
if docker info &> /dev/null; then
    print_status "Docker daemon is running"
else
    print_error "Docker daemon is not running"
    exit 1
fi

# Check available ports
print_info "Checking port availability..."
ports=(8080 8081 4723 4724)
for port in "${ports[@]}"; do
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_warning "Port $port is already in use"
    else
        print_status "Port $port is available"
    fi
done

# Check available disk space
print_info "Checking disk space..."
available_space=$(df -BG . | awk 'NR==2 {print $4}' | sed 's/G//')
if [ "$available_space" -gt 5 ]; then
    print_status "Sufficient disk space available: ${available_space}GB"
else
    print_warning "Low disk space: ${available_space}GB (recommend at least 5GB)"
fi

# Check available memory
print_info "Checking available memory..."
if command -v free &> /dev/null; then
    available_memory=$(free -g | awk 'NR==2{printf "%.0f", $7}')
    if [ "$available_memory" -gt 3 ]; then
        print_status "Sufficient memory available: ${available_memory}GB"
    else
        print_warning "Low memory: ${available_memory}GB (recommend at least 4GB)"
    fi
else
    print_info "Memory check not available on this system"
fi

# Check if host data directories exist
print_info "Checking host data directories..."
if [ -d "host-data" ]; then
    print_status "Host data directory exists"
    
    subdirs=(test_cases test_suites reports screenshots reference_images recordings temp files_to_push app-data app-android-data data)
    for subdir in "${subdirs[@]}"; do
        if [ -d "host-data/$subdir" ]; then
            print_status "host-data/$subdir exists"
        else
            print_warning "host-data/$subdir does not exist (will be created during deployment)"
        fi
    done
else
    print_warning "Host data directory does not exist (will be created during deployment)"
fi

# Check if Dockerfile exists
print_info "Checking Docker configuration files..."
if [ -f "Dockerfile" ]; then
    print_status "Dockerfile exists"
else
    print_error "Dockerfile not found"
    exit 1
fi

if [ -f "docker-compose.yml" ]; then
    print_status "docker-compose.yml exists"
else
    print_error "docker-compose.yml not found"
    exit 1
fi

# Check if deployment script exists
if [ -f "deploy-docker.sh" ]; then
    print_status "Deployment script exists"
    if [ -x "deploy-docker.sh" ]; then
        print_status "Deployment script is executable"
    else
        print_warning "Deployment script is not executable (run: chmod +x deploy-docker.sh)"
    fi
else
    print_warning "Deployment script not found"
fi

# Test Docker Compose configuration
print_info "Validating Docker Compose configuration..."
if docker-compose config &> /dev/null; then
    print_status "Docker Compose configuration is valid"
else
    print_error "Docker Compose configuration has errors"
    docker-compose config
    exit 1
fi

echo
echo -e "${GREEN}=== Validation Complete ===${NC}"
echo
echo -e "${BLUE}Next steps:${NC}"
echo "1. Run: ./deploy-docker.sh"
echo "2. Wait for deployment to complete"
echo "3. Access iOS platform at: http://localhost:8080"
echo "4. Access Android platform at: http://localhost:8081"
echo
echo -e "${BLUE}If you encounter issues:${NC}"
echo "- Check the logs: docker-compose logs -f"
echo "- Verify device connectivity on host machine first"
echo "- Ensure all required ports are available"