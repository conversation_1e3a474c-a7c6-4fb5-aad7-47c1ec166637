# Example Docker Compose Override File
# Copy this to docker-compose.override.yml and customize as needed

services:
  mobile-automation-tool:
    # Uncomment to use specific resource limits
    # deploy:
    #   resources:
    #     limits:
    #       cpus: '4.0'
    #       memory: 8G
    #     reservations:
    #       cpus: '2.0'
    #       memory: 4G
    
    # Uncomment to add custom environment variables
    # environment:
    #   - FLASK_DEBUG=false
    #   - APPIUM_LOG_LEVEL=info
    #   - CUSTOM_TIMEOUT=300
    
    # Uncomment to use bridge network instead of host network
    # network_mode: "bridge"
    # ports:
    #   - "8080:8080"
    #   - "8081:8081"
    #   - "4723:4723"
    #   - "4724:4724"
    
    # Uncomment to add additional volume mounts
    # volumes:
    #   - ./custom-config:/app/custom-config
    #   - ./additional-data:/app/additional-data
    
    # Uncomment to change restart policy
    # restart: always