#!/bin/bash

# Health check script for Mobile App Automation Tool Docker container

# Check if iOS platform is responding
ios_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/ || echo "000")

# Check if Android platform is responding  
android_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8081/ || echo "000")

# Check if Appium servers are responding
appium_ios_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:4723/wd/hub/status || echo "000")
appium_android_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:4724/wd/hub/status || echo "000")

echo "Health Check Status:"
echo "iOS Platform (8080): $ios_status"
echo "Android Platform (8081): $android_status"
echo "Appium iOS (4723): $appium_ios_status"
echo "Appium Android (4724): $appium_android_status"

# Consider healthy if at least one platform and one Appium server are responding
if [[ "$ios_status" == "200" || "$android_status" == "200" ]] && [[ "$appium_ios_status" == "200" || "$appium_android_status" == "200" ]]; then
    echo "Container is healthy"
    exit 0
else
    echo "Container is unhealthy"
    exit 1
fi