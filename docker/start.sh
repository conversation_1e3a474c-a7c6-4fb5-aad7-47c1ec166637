#!/bin/bash

# Mobile App Automation Tool - Docker Startup Script

set -e

echo "Starting Mobile App Automation Tool Docker Container..."

# Activate Python virtual environment
source /opt/venv/bin/activate

# Set environment variables
export JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64
export ANDROID_HOME=/opt/android-sdk
export ANDROID_SDK_ROOT=/opt/android-sdk
export PATH=$PATH:$ANDROID_HOME/platform-tools:$ANDROID_HOME/build-tools/33.0.0

# Create log directory for supervisor
mkdir -p /var/log/supervisor

# Ensure all necessary directories exist and have proper permissions
mkdir -p \
    /app/test_cases \
    /app/test_suites \
    /app/reports \
    /app/screenshots \
    /app/reference_images \
    /app/recordings \
    /app/temp \
    /app/files_to_push \
    /app/app/data \
    /app/app_android/data \
    /app/data

# Set proper permissions for data directories
chmod -R 755 /app/test_cases /app/test_suites /app/reports /app/screenshots /app/reference_images /app/recordings /app/temp /app/files_to_push
chmod -R 755 /app/app/data /app/app_android/data /app/data

# Initialize databases if they don't exist
cd /app

# Wait a moment for any file system operations to complete
sleep 2

echo "Environment setup complete. Starting services..."

# Print configuration information
echo "=== Mobile App Automation Tool Configuration ==="
echo "iOS Platform: http://localhost:8080"
echo "Android Platform: http://localhost:8081"
echo "Appium iOS Server: http://localhost:4723"
echo "Appium Android Server: http://localhost:4724"
echo "Java Home: $JAVA_HOME"
echo "Android Home: $ANDROID_HOME"
echo "Python Virtual Environment: /opt/venv"
echo "=============================================="

# Start supervisor to manage all services
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf