# Mobile App Automation Tool - Docker Deployment Guide

This Docker solution provides a complete containerized environment for the Mobile App Automation Tool, supporting both iOS and Android testing platforms simultaneously.

## Features

- **Dual Platform Support**: iOS testing interface on port 8080, Android testing interface on port 8081
- **Complete Environment**: All dependencies pre-installed (Python, Node.js, Appium, Java, Android SDK, Tesseract OCR)
- **Volume Mounting**: Host directories can be mounted for test cases, reports, and other data
- **Health Monitoring**: Built-in health checks for all services
- **Process Management**: Supervisor manages all services (Appium servers and web interfaces)

## Quick Start

### Option 1: Using Docker Compose (Recommended)

1. **Build and start the container:**
   ```bash
   docker-compose up --build
   ```

2. **Access the platforms:**
   - iOS Testing Platform: http://localhost:8080
   - Android Testing Platform: http://localhost:8081

### Option 2: Using Docker Commands

1. **Build the image:**
   ```bash
   docker build -t mobile-automation-tool .
   ```

2. **Run the container:**
   ```bash
   docker run -d \
     --name mobile-automation-tool \
     -p 8080:8080 \
     -p 8081:8081 \
     -p 4723:4723 \
     -p 4724:4724 \
     -v $(pwd)/host-data/test_cases:/app/test_cases \
     -v $(pwd)/host-data/test_suites:/app/test_suites \
     -v $(pwd)/host-data/reports:/app/reports \
     -v $(pwd)/host-data/screenshots:/app/screenshots \
     -v $(pwd)/host-data/reference_images:/app/reference_images \
     -v $(pwd)/host-data/recordings:/app/recordings \
     -v $(pwd)/host-data/temp:/app/temp \
     -v $(pwd)/host-data/files_to_push:/app/files_to_push \
     -v $(pwd)/host-data/app-data:/app/app/data \
     -v $(pwd)/host-data/app-android-data:/app/app_android/data \
     -v $(pwd)/host-data/data:/app/data \
     mobile-automation-tool
   ```

## Volume Mounting Configuration

### Host Directory Structure

Create the following directory structure on your host machine:

```
host-data/
├── test_cases/          # Your test case files
├── test_suites/         # Your test suite configurations
├── reports/             # Generated test reports
├── screenshots/         # Screenshots taken during tests
├── reference_images/    # Reference images for image matching
├── recordings/          # Test execution recordings
├── temp/               # Temporary files
├── files_to_push/      # Files to push to devices
├── app-data/           # iOS platform database files
├── app-android-data/   # Android platform database files
└── data/               # Shared database files
```

### Creating Host Directories

Run this command to create all necessary directories:

```bash
mkdir -p host-data/{test_cases,test_suites,reports,screenshots,reference_images,recordings,temp,files_to_push,app-data,app-android-data,data}
```

### Configuring Folder Paths in Settings

Once the container is running:

1. Access the iOS platform at http://localhost:8080
2. Go to the Settings tab
3. Configure the folder paths to point to the mounted directories:
   - Test Cases Folder: `/app/test_cases`
   - Test Suites Folder: `/app/test_suites`
   - Reports Folder: `/app/reports`
   - Screenshots Folder: `/app/screenshots`
   - Reference Images Folder: `/app/reference_images`
   - Recordings Folder: `/app/recordings`
   - Temp Files Folder: `/app/temp`
   - Files to Push Folder: `/app/files_to_push`

4. Repeat the same configuration for the Android platform at http://localhost:8081

## Service Ports

| Service | Port | Description |
|---------|------|-------------|
| iOS Platform | 8080 | Web interface for iOS testing |
| Android Platform | 8081 | Web interface for Android testing |
| Appium iOS | 4723 | Appium server for iOS devices |
| Appium Android | 4724 | Appium server for Android devices |

## Device Connectivity

### For iOS Devices (macOS Host Required)

iOS device testing requires a macOS host machine. The container uses `network_mode: "host"` to enable device connectivity.

1. Connect your iOS device to the macOS host
2. Ensure the device is trusted and appears in Xcode
3. The container will be able to access the device through the host network

### For Android Devices

Android devices can be connected via USB or network ADB:

1. **USB Connection**: Connect device to host machine
2. **Network ADB**: Configure device for network debugging
3. The container accesses devices through the host's ADB daemon

## Container Management

### View Logs
```bash
# All services
docker-compose logs

# Specific service
docker-compose logs mobile-automation-tool

# Follow logs in real-time
docker-compose logs -f
```

### Check Container Health
```bash
docker-compose ps
```

### Stop and Remove
```bash
docker-compose down
```

### Rebuild After Changes
```bash
docker-compose down
docker-compose up --build
```

## Troubleshooting

### Container Won't Start
1. Check Docker logs: `docker-compose logs`
2. Verify all required directories exist
3. Ensure ports 8080, 8081, 4723, 4724 are not in use

### Can't Access Web Interface
1. Verify container is running: `docker-compose ps`
2. Check port mapping in docker-compose.yml
3. Ensure firewall allows connections to the ports

### Device Not Detected
1. Verify device is connected to host machine
2. Check USB debugging is enabled (Android)
3. Ensure device is trusted (iOS)
4. Verify network_mode: "host" is configured

### Database Issues
1. Check volume mounts are correctly configured
2. Verify host directories have proper permissions
3. Check database files in mounted volumes

## Advanced Configuration

### Custom Environment Variables

Add environment variables to docker-compose.yml:

```yaml
environment:
  - CUSTOM_VAR=value
  - FLASK_DEBUG=true
```

### Custom Network Configuration

For isolated networking, replace `network_mode: "host"` with:

```yaml
networks:
  - automation-network

networks:
  automation-network:
    driver: bridge
```

### Resource Limits

Add resource constraints:

```yaml
deploy:
  resources:
    limits:
      cpus: '2.0'
      memory: 4G
    reservations:
      cpus: '1.0'
      memory: 2G
```

## Security Considerations

- The container runs with elevated privileges for device access
- Ensure host directories have appropriate permissions
- Consider using secrets management for sensitive configuration
- Regularly update the base image and dependencies

## Support

For issues with the Docker deployment:
1. Check the container logs
2. Verify all prerequisites are met
3. Ensure proper volume mounting configuration
4. Test device connectivity on the host machine first