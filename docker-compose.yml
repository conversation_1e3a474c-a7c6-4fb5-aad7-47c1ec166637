
services:
  mobile-automation-tool:
    build: .
    container_name: mobile-automation-tool
    ports:
      # iOS Platform Web Interface
      - "8080:8080"
      # Android Platform Web Interface  
      - "8081:8081"
      # Appium Server for iOS
      - "4723:4723"
      # Appium Server for Android
      - "4724:4724"
    volumes:
      # Mount host directories for test data and results
      # Modify these paths to match your host machine directories
      - ./host-data/test_cases:/app/test_cases
      - ./host-data/test_suites:/app/test_suites
      - ./host-data/reports:/app/reports
      - ./host-data/screenshots:/app/screenshots
      - ./host-data/reference_images:/app/reference_images
      - ./host-data/recordings:/app/recordings
      - ./host-data/temp:/app/temp
      - ./host-data/files_to_push:/app/files_to_push
      # Mount database directories to persist data
      - ./host-data/app-data:/app/app/data
      - ./host-data/app-android-data:/app/app_android/data
      - ./host-data/data:/app/data
    environment:
      # Java and Android environment variables
      - JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64
      - ANDROID_HOME=/opt/android-sdk
      - ANDROID_SDK_ROOT=/opt/android-sdk
      # Python environment
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "/healthcheck.sh"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    # Use bridge network with explicit port mappings (better compatibility)
    networks:
      - automation-network

networks:
  automation-network:
    driver: bridge