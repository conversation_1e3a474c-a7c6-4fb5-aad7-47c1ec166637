# Git and version control
.git
.gitignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.venv
venv/

# Node.js
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE and editor files
.vscode
.idea
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Documentation
docs/
*.md
README.md

# Test and development files
test/
tests/
*.test.js

# Logs
*.log
logs/
appium_server.log
iproxy.log

# Temporary files
temp/
tmp/
*.tmp

# Database files (will be created in container)
*.db
*.sqlite
*.sqlite3

# Screenshots and media (will be mounted)
screenshots/
recordings/
reference_images/
debug_images/
temp-pics-debug/

# Reports (will be mounted)
reports/

# Host data directory (for volume mounting)
host-data/

# Memory bank and documentation
memory-bank/
image_comparison/
temp_text_detection/

# Scripts that aren't needed in container
scripts/
*.sh
!docker/*.sh

# Configuration files that might contain sensitive data
cookies.txt
sessions/

# Build artifacts
build/
dist/
*.egg-info/

# Docker files (except the ones we need)
Dockerfile.*
docker-compose.override.yml