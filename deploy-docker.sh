#!/bin/bash

# Mobile App Automation Tool - Docker Deployment Script
# This script helps you deploy the containerized automation tool

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker and try again."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose and try again."
        exit 1
    fi
    
    print_status "Docker and <PERSON>er Compose are installed"
}

# Create host data directories
create_host_directories() {
    print_header "Creating Host Data Directories"
    
    mkdir -p host-data/{test_cases,test_suites,reports,screenshots,reference_images,recordings,temp,files_to_push,app-data,app-android-data,data}
    
    print_status "Created host data directories:"
    print_status "  - host-data/test_cases (for your test case files)"
    print_status "  - host-data/test_suites (for test suite configurations)"
    print_status "  - host-data/reports (for generated reports)"
    print_status "  - host-data/screenshots (for screenshots)"
    print_status "  - host-data/reference_images (for image matching)"
    print_status "  - host-data/recordings (for test recordings)"
    print_status "  - host-data/temp (for temporary files)"
    print_status "  - host-data/files_to_push (for files to push to devices)"
    print_status "  - host-data/app-data (for iOS platform database)"
    print_status "  - host-data/app-android-data (for Android platform database)"
    print_status "  - host-data/data (for shared database files)"
}

# Build and start the container
deploy_container() {
    print_header "Building and Starting Container"
    
    print_status "Building Docker image (this may take several minutes)..."
    docker-compose build
    
    print_status "Starting container..."
    docker-compose up -d
    
    print_status "Container started successfully!"
}

# Check container health
check_health() {
    print_header "Checking Container Health"
    
    print_status "Waiting for services to start (this may take up to 2 minutes)..."
    sleep 30
    
    # Check if container is running
    if docker-compose ps | grep -q "Up"; then
        print_status "Container is running"
    else
        print_error "Container failed to start. Check logs with: docker-compose logs"
        exit 1
    fi
    
    # Wait for services to be ready
    local max_attempts=12
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        print_status "Health check attempt $attempt/$max_attempts..."
        
        # Check iOS platform
        ios_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/ || echo "000")
        
        # Check Android platform
        android_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8081/ || echo "000")
        
        if [[ "$ios_status" == "200" ]] && [[ "$android_status" == "200" ]]; then
            print_status "Both platforms are healthy and ready!"
            break
        elif [[ "$ios_status" == "200" ]] || [[ "$android_status" == "200" ]]; then
            print_status "At least one platform is healthy. Still checking..."
        else
            print_warning "Services not ready yet. Waiting..."
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            print_warning "Services may still be starting. Check manually or view logs."
            break
        fi
        
        sleep 10
        ((attempt++))
    done
}

# Display access information
show_access_info() {
    print_header "Access Information"
    
    echo -e "${GREEN}Your Mobile App Automation Tool is now running!${NC}"
    echo ""
    echo -e "${BLUE}Web Interfaces:${NC}"
    echo -e "  📱 iOS Testing Platform:     ${GREEN}http://localhost:8080${NC}"
    echo -e "  🤖 Android Testing Platform: ${GREEN}http://localhost:8081${NC}"
    echo ""
    echo -e "${BLUE}Appium Servers:${NC}"
    echo -e "  📱 iOS Appium Server:        ${GREEN}http://localhost:4723${NC}"
    echo -e "  🤖 Android Appium Server:    ${GREEN}http://localhost:4724${NC}"
    echo ""
    echo -e "${BLUE}Host Data Directories:${NC}"
    echo -e "  📁 Test Cases:     ${YELLOW}$(pwd)/host-data/test_cases${NC}"
    echo -e "  📁 Test Suites:    ${YELLOW}$(pwd)/host-data/test_suites${NC}"
    echo -e "  📁 Reports:        ${YELLOW}$(pwd)/host-data/reports${NC}"
    echo -e "  📁 Screenshots:    ${YELLOW}$(pwd)/host-data/screenshots${NC}"
    echo -e "  📁 Reference Images: ${YELLOW}$(pwd)/host-data/reference_images${NC}"
    echo ""
    echo -e "${BLUE}Next Steps:${NC}"
    echo "1. Open your web browser and navigate to the platforms above"
    echo "2. Go to Settings tab in each platform to configure folder paths"
    echo "3. Set folder paths to the mounted directories (e.g., /app/test_cases)"
    echo "4. Connect your iOS/Android devices to the host machine"
    echo "5. Start creating and running your test cases!"
    echo ""
    echo -e "${BLUE}Useful Commands:${NC}"
    echo -e "  View logs:           ${YELLOW}docker-compose logs -f${NC}"
    echo -e "  Stop container:      ${YELLOW}docker-compose down${NC}"
    echo -e "  Restart container:   ${YELLOW}docker-compose restart${NC}"
    echo -e "  Check status:        ${YELLOW}docker-compose ps${NC}"
}

# Main deployment function
main() {
    print_header "Mobile App Automation Tool - Docker Deployment"
    
    # Check prerequisites
    check_docker
    
    # Create directories
    create_host_directories
    
    # Deploy container
    deploy_container
    
    # Check health
    check_health
    
    # Show access information
    show_access_info
    
    print_status "Deployment completed successfully! 🎉"
}

# Handle command line arguments
case "${1:-}" in
    "build")
        print_header "Building Docker Image Only"
        docker-compose build
        print_status "Build completed!"
        ;;
    "start")
        print_header "Starting Container"
        docker-compose up -d
        check_health
        show_access_info
        ;;
    "stop")
        print_header "Stopping Container"
        docker-compose down
        print_status "Container stopped!"
        ;;
    "restart")
        print_header "Restarting Container"
        docker-compose restart
        check_health
        show_access_info
        ;;
    "logs")
        print_header "Viewing Container Logs"
        docker-compose logs -f
        ;;
    "status")
        print_header "Container Status"
        docker-compose ps
        ;;
    "clean")
        print_header "Cleaning Up"
        docker-compose down
        docker system prune -f
        print_status "Cleanup completed!"
        ;;
    "help"|"-h"|"--help")
        echo "Mobile App Automation Tool - Docker Deployment Script"
        echo ""
        echo "Usage: $0 [command]"
        echo ""
        echo "Commands:"
        echo "  (no command)  - Full deployment (build, start, check health)"
        echo "  build         - Build Docker image only"
        echo "  start         - Start the container"
        echo "  stop          - Stop the container"
        echo "  restart       - Restart the container"
        echo "  logs          - View container logs"
        echo "  status        - Check container status"
        echo "  clean         - Stop container and clean up Docker resources"
        echo "  help          - Show this help message"
        ;;
    "")
        # No arguments - run full deployment
        main
        ;;
    *)
        print_error "Unknown command: $1"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac